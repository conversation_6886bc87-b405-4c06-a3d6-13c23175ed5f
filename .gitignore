# Xcode
#
# gitignore contributors: remember to update Global/Xcode.gitignore, Objective-C.gitignore & Swift.gitignore

## macOS系统文件
.DS_Store
.AppleDouble
.LSOverride

## 用户设置
xcuserdata/

## 构建产物
DerivedData/
build/
*.hmap

## App打包
*.ipa
*.dSYM.zip
*.dSYM

## Playgrounds
timeline.xctimeline
playground.xcworkspace

# Swift Package Manager
#
# SPM包依赖管理
.build/
.swiftpm/
Package.resolved

# 如果你使用的是纯SPM项目，可以考虑忽略以下文件：
# Packages/
# Package.pins
# *.xcodeproj

# CocoaPods
#
# 如果项目同时使用CocoaPods，请取消注释以下行：
# Pods/
# *.xcworkspace

# Carthage
#
# 如果使用Carthage，请取消注释以下行：
# Carthage/Checkouts
Carthage/Build/

# fastlane
#
# 自动化构建和部署工具
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# 代码覆盖率
*.profraw
*.profdata

# 临时文件
*.tmp
*.temp
*~

# IDE 文件
.vscode/
.idea/

# 调试信息
*.log
