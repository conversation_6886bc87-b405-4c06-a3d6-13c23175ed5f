import SwiftUI

/// 流式布局组件，支持自动换行的布局方式
/// 使用示例：
/// ```swift
/// FlowLayout(alignment: .leading, spacing: 8) {
///     ForEach(items) { item in
///         Text(item.title)
///             .padding(.horizontal, 8)
///             .padding(.vertical, 4)
///     }
/// }
/// ```
struct FlowLayout: Layout {
    var alignment: HorizontalAlignment = .leading
    var spacing: CGFloat = 0
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let result = FlowResult(
            in: proposal.width ?? 0,
            subviews: subviews,
            alignment: alignment,
            spacing: spacing
        )
        return result.size
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let result = FlowResult(
            in: bounds.width,
            subviews: subviews,
            alignment: alignment,
            spacing: spacing
        )
        for (index, line) in result.lines.enumerated() {
            let yOffset = result.lineOffsets[index]
            for item in line.subviews {
                let point = CGPoint(
                    x: bounds.minX + item.offset,
                    y: bounds.minY + yOffset
                )
                item.subview.place(at: point, proposal: .unspecified)
            }
        }
    }
    
    private struct FlowResult {
        struct Line {
            var subviews: [(offset: CGFloat, subview: LayoutSubview)]
            var spacing: CGFloat
            var height: CGFloat
        }
        
        var lines: [Line] = []
        var lineOffsets: [CGFloat] = []
        var size: CGSize = .zero
        
        init(in maxWidth: CGFloat, subviews: LayoutSubviews, alignment: HorizontalAlignment, spacing: CGFloat) {
            var lines: [Line] = []
            var currentLine = Line(subviews: [], spacing: spacing, height: 0)
            var currentX: CGFloat = 0
            
            for subview in subviews {
                let size = subview.sizeThatFits(.unspecified)
                if currentX + size.width > maxWidth, !currentLine.subviews.isEmpty {
                    lines.append(currentLine)
                    currentLine = Line(subviews: [], spacing: spacing, height: 0)
                    currentX = 0
                }
                
                currentLine.subviews.append((offset: currentX, subview: subview))
                currentLine.height = max(currentLine.height, size.height)
                currentX += size.width + spacing
            }
            
            if !currentLine.subviews.isEmpty {
                lines.append(currentLine)
            }
            
            self.lines = lines
            
            var yOffset: CGFloat = 0
            var maxWidth: CGFloat = 0
            var lineOffsets: [CGFloat] = []
            
            for line in lines {
                lineOffsets.append(yOffset)
                yOffset += line.height + spacing
                maxWidth = max(maxWidth, line.subviews.last.map { $0.offset + $0.subview.sizeThatFits(.unspecified).width } ?? 0)
            }
            
            self.lineOffsets = lineOffsets
            self.size = CGSize(width: maxWidth, height: yOffset - (lines.isEmpty ? 0 : spacing))
        }
    }
} 