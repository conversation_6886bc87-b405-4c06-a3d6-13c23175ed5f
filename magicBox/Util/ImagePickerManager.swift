import UIKit
import YPImagePicker
import PermissionsKit
import PhotoLibraryPermission

class ImagePickerManager {
    // 单例模式
    static let shared = ImagePickerManager()
    private init() {}
    
    // 图片选择完成的回调类型
    typealias ImagePickerCompletion = ([UIImage]) -> Void
    
    // 配置 YPImagePicker
    private var defaultConfig: YPImagePickerConfiguration {
        var config = YPImagePickerConfiguration()
        config.library.maxNumberOfItems = 30    // 最多选择30张图片
        config.library.mediaType = .photo       // 只显示照片
        config.screens = [.library]             // 只显示相册
        config.library.defaultMultipleSelection = true   // 默认多选
        config.library.skipSelectionsGallery = false    // 显示选择预览界面
        config.library.preselectedItems = nil   // 默认不预选
        
        // 禁用滤镜
        config.showsPhotoFilters = false
        config.startOnScreen = .library
        config.hidesStatusBar = false
        config.hidesBottomBar = false
        
        return config
    }
    
    /// 显示图片选择器
    /// - Parameters:
    ///   - viewController: 用于显示图片选择器的视图控制器
    ///   - maxItems: 最大可选择的图片数量，默认为30
    ///   - completion: 选择完成的回调，返回选中的图片数组（如果用户取消则返回空数组）
    func showImagePicker(
        from viewController: UIViewController,
        maxItems: Int = 30,
        completion: @escaping ImagePickerCompletion
    ) {
        // 先检查相册权限
        Permission.photoLibrary.request { [weak viewController] in
            DispatchQueue.main.async {
                if Permission.photoLibrary.authorized {
                    // 用户授权，显示图片选择器
                    self.presentImagePicker(
                        from: viewController,
                        maxItems: maxItems,
                        completion: completion
                    )
                } else {
                    // 用户拒绝，显示提示
                    self.showPermissionDeniedAlert(from: viewController)
                }
            }
        }
    }
    
    // 显示图片选择器
    private func presentImagePicker(
        from viewController: UIViewController?,
        maxItems: Int,
        completion: @escaping ImagePickerCompletion
    ) {
        guard let viewController = viewController else { return }
        
        var config = defaultConfig
        config.library.maxNumberOfItems = maxItems
        
        let picker = YPImagePicker(configuration: config)
        picker.didFinishPicking { items, cancelled in
            if cancelled {
                completion([])
                picker.dismiss(animated: true)
                return
            }
            
            let selectedImages = items.compactMap { item -> UIImage? in
                if case let .photo(photo) = item {
                    return photo.image
                }
                return nil
            }
            
            completion(selectedImages)
            picker.dismiss(animated: true)
        }
        
        viewController.present(picker, animated: true)
    }
    
    // 显示权限被拒绝的提示
    private func showPermissionDeniedAlert(from viewController: UIViewController?) {
        guard let viewController = viewController else { return }
        
        let alert = UIAlertController(
            title: NSLocalizedString("permission.denied.title", comment: "Permission Denied"),
            message: NSLocalizedString("permission.photos.denied.message", comment: "Please enable photos access in Settings"),
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("common.ok", comment: "OK"),
            style: .default
        ))
        
        viewController.present(alert, animated: true)
    }
} 