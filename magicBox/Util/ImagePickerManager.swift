import UIKit
import ZLPhotoBrowser
import PermissionsKit
import PhotoLibraryPermission

class ImagePickerManager {
    // 单例模式
    static let shared = ImagePickerManager()
    private init() {}
    
    // 图片选择完成的回调类型
    typealias ImagePickerCompletion = ([UIImage]) -> Void

    // 配置 ZLPhotoBrowser
    private func configureZLPhotoBrowser(maxItems: Int) {
        // 基础配置
        ZLPhotoConfiguration.default()
            .maxSelectCount(maxItems)           // 最大选择数量
            .allowSelectImage(true)             // 允许选择图片
            .allowSelectVideo(false)            // 不允许选择视频
            .allowSelectGif(true)               // 允许选择GIF
            .allowSelectLivePhoto(false)        // 不允许选择LivePhoto
            .allowTakePhotoInLibrary(false)     // 不允许在相册内拍照
            .allowEditImage(false)              // 不允许编辑图片
            .allowSlideSelect(true)             // 允许滑动选择
            .allowDragSelect(true)              // 允许拖拽选择
            .allowMixSelect(false)              // 不允许图片和视频混选
            .maxPreviewCount(20)                // 最大预览数量
            .cellCornerRadio(0)                 // 圆角半径

        // UI配置
        ZLPhotoUIConfiguration.default()
            .themeColor(.systemBlue)            // 主题色
            .navBarColor(.systemBackground)     // 导航栏颜色
            .navTitleColor(.label)              // 导航栏标题颜色
            .bottomToolViewBgColor(.systemBackground) // 底部工具栏背景色
    }
    
    /// 显示图片选择器
    /// - Parameters:
    ///   - viewController: 用于显示图片选择器的视图控制器
    ///   - maxItems: 最大可选择的图片数量，默认为30
    ///   - completion: 选择完成的回调，返回选中的图片数组（如果用户取消则返回空数组）
    func showImagePicker(
        from viewController: UIViewController,
        maxItems: Int = 30,
        completion: @escaping ImagePickerCompletion
    ) {
        // 先检查相册权限
        Permission.photoLibrary.request { [weak viewController] in
            DispatchQueue.main.async {
                if Permission.photoLibrary.authorized {
                    // 用户授权，显示图片选择器
                    self.presentImagePicker(
                        from: viewController,
                        maxItems: maxItems,
                        completion: completion
                    )
                } else {
                    // 用户拒绝，显示提示
                    self.showPermissionDeniedAlert(from: viewController)
                }
            }
        }
    }
    
    // 显示图片选择器
    private func presentImagePicker(
        from viewController: UIViewController?,
        maxItems: Int,
        completion: @escaping ImagePickerCompletion
    ) {
        guard let viewController = viewController else { return }

        // 配置 ZLPhotoBrowser
        configureZLPhotoBrowser(maxItems: maxItems)

        let picker = ZLPhotoPreviewSheet()
        picker.selectImageBlock = { [weak self] results, isOriginal in
            // 将 ZLResultModel 转换为 UIImage 数组
            let selectedImages = results.compactMap { result -> UIImage? in
                return result.image
            }
            completion(selectedImages)
        }

        // 显示相册选择界面
        picker.showPhotoLibrary(sender: viewController)
    }
    
    // 显示权限被拒绝的提示
    private func showPermissionDeniedAlert(from viewController: UIViewController?) {
        guard let viewController = viewController else { return }
        
        let alert = UIAlertController(
            title: NSLocalizedString("permission.denied.title", comment: "Permission Denied"),
            message: NSLocalizedString("permission.photos.denied.message", comment: "Please enable photos access in Settings"),
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("common.ok", comment: "OK"),
            style: .default
        ))
        
        viewController.present(alert, animated: true)
    }
} 