//
//  Logger.swift
//  magicBox
//
//  Created by AI Assistant on 2025/1/19.
//

import Foundation

// MARK: - 日志级别枚举
enum LogLevel: String, CaseIterable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    case verbose = "VERBOSE"
    
    /// 日志级别的emoji标识
    var emoji: String {
        switch self {
        case .debug:
            return "🐛"
        case .info:
            return "ℹ️"
        case .warning:
            return "⚠️"
        case .error:
            return "❌"
        case .verbose:
            return "📝"
        }
    }
}

// MARK: - 日志输出工具类
class Logger {
    
    // MARK: - 静态属性
    /// 是否启用日志输出（默认只在Debug模式下启用）
    static var isEnabled: Bool = {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }()
    
    /// 最低日志级别（低于此级别的日志不会输出）
    static var minimumLogLevel: LogLevel = .debug
    
    /// 是否显示时间戳
    static var showTimestamp: Bool = true
    
    /// 是否显示文件名和行号
    static var showFileInfo: Bool = false
    
    /// 是否显示函数名
    static var showFunctionName: Bool = false
    
    /// 全局前缀（应用于所有日志）
    static var globalPrefix: String = "MagicBox"
    
    /// 是否显示全局前缀
    static var showGlobalPrefix: Bool = false
    
    // MARK: - 实例属性
    /// 业务前缀（特定业务模块的前缀）
    private let businessPrefix: String?
    
    // MARK: - 时间格式化器
    private static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        return formatter
    }()
    
    // MARK: - 初始化方法
    /// 创建带业务前缀的Logger实例
    /// - Parameter businessPrefix: 业务前缀，如 "Database", "UI", "Network" 等
    init(businessPrefix: String? = nil) {
        self.businessPrefix = businessPrefix
    }
    
    /// 私有静态实例（用于静态方法）
    private static let shared = Logger()
    
    // MARK: - 核心日志输出方法
    /// 核心日志输出方法
    /// - Parameters:
    ///   - level: 日志级别
    ///   - message: 日志消息
    ///   - prefix: 临时前缀（可选）
    ///   - file: 文件名
    ///   - function: 函数名
    ///   - line: 行号
    private func log(
        level: LogLevel,
        message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        // 检查是否启用日志输出
        guard Self.isEnabled else { return }
        
        // 检查日志级别
        guard Self.shouldLog(level: level) else { return }
        
        // 构建日志消息
        let logMessage = buildLogMessage(
            level: level,
            message: message,
            prefix: prefix,
            file: file,
            function: function,
            line: line
        )
        
        // 使用NSLog输出
        NSLog("%@", logMessage)
    }
    
    // MARK: - 日志级别检查
    private static func shouldLog(level: LogLevel) -> Bool {
        let levels: [LogLevel] = [.debug, .verbose, .info, .warning, .error]
        guard let currentIndex = levels.firstIndex(of: minimumLogLevel),
              let targetIndex = levels.firstIndex(of: level) else {
            return true
        }
        return targetIndex >= currentIndex
    }
    
    // MARK: - 构建日志消息
    private func buildLogMessage(
        level: LogLevel,
        message: String,
        prefix: String?,
        file: String,
        function: String,
        line: Int
    ) -> String {
        var components: [String] = []
        
        // 添加前缀信息
        var prefixComponents: [String] = []
        
        // 添加全局前缀
        if Self.showGlobalPrefix {
            prefixComponents.append(Self.globalPrefix)
        }
        
        // 添加业务前缀
        if let businessPrefix = businessPrefix {
            prefixComponents.append(businessPrefix)
        }
        
        // 添加临时前缀
        if let prefix = prefix {
            prefixComponents.append(prefix)
        }
        
        // 组合前缀
        if !prefixComponents.isEmpty {
            let combinedPrefix = prefixComponents.joined(separator: ".")
            components.append("[\(combinedPrefix)]")
        }
        
        // 添加日志级别和emoji
        components.append("[\(level.emoji) \(level.rawValue)]")
        
        // 添加时间戳
        if Self.showTimestamp {
            components.append("[\(Self.dateFormatter.string(from: Date()))]")
        }
        
        // 添加文件信息
        if Self.showFileInfo {
            let fileName = URL(fileURLWithPath: file).lastPathComponent
            components.append("[\(fileName):\(line)]")
        }
        
        // 添加函数名
        if Self.showFunctionName {
            components.append("[\(function)]")
        }
        
        // 添加消息
        components.append(message)
        
        return components.joined(separator: " ")
    }
    
    // MARK: - 实例日志方法
    /// Debug级别日志
    func debug(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .debug, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// Info级别日志
    func info(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .info, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// Warning级别日志
    func warning(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .warning, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// Error级别日志
    func error(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .error, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// Verbose级别日志
    func verbose(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .verbose, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    // MARK: - 静态日志方法（保持向后兼容）
    /// 静态Debug级别日志
    static func debug(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.log(level: .debug, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态Info级别日志
    static func info(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.log(level: .info, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态Warning级别日志
    static func warning(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.log(level: .warning, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态Error级别日志
    static func error(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.log(level: .error, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态Verbose级别日志
    static func verbose(
        _ message: String,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.log(level: .verbose, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    // MARK: - 格式化日志方法
    /// 格式化Debug日志
    func debug(
        _ format: String,
        _ args: CVarArg...,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = String(format: format, arguments: args)
        log(level: .debug, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 格式化Info日志
    func info(
        _ format: String,
        _ args: CVarArg...,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = String(format: format, arguments: args)
        log(level: .info, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 格式化Warning日志
    func warning(
        _ format: String,
        _ args: CVarArg...,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = String(format: format, arguments: args)
        log(level: .warning, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 格式化Error日志
    func error(
        _ format: String,
        _ args: CVarArg...,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = String(format: format, arguments: args)
        log(level: .error, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    // MARK: - 静态格式化日志方法
    /// 静态格式化Debug日志
    static func debug(
        _ format: String,
        _ args: CVarArg...,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = String(format: format, arguments: args)
        shared.log(level: .debug, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态格式化Info日志
    static func info(
        _ format: String,
        _ args: CVarArg...,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = String(format: format, arguments: args)
        shared.log(level: .info, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态格式化Warning日志
    static func warning(
        _ format: String,
        _ args: CVarArg...,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = String(format: format, arguments: args)
        shared.log(level: .warning, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态格式化Error日志
    static func error(
        _ format: String,
        _ args: CVarArg...,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = String(format: format, arguments: args)
        shared.log(level: .error, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    // MARK: - 特殊用途的日志方法
    /// 打印分割线
    func separator(
        _ title: String = "",
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let separator = String(repeating: "=", count: 50)
        let message = title.isEmpty ? separator : "\(separator) \(title) \(separator)"
        log(level: .info, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态打印分割线
    static func separator(
        _ title: String = "",
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.separator(title, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 打印对象信息
    func object(
        _ object: Any,
        name: String = "Object",
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = "\(name): \(object)"
        log(level: .debug, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态打印对象信息
    static func object(
        _ object: Any,
        name: String = "Object",
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.object(object, name: name, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 打印函数进入
    func enter(
        _ functionName: String = #function,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = "→ 进入函数: \(functionName)"
        log(level: .verbose, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态打印函数进入
    static func enter(
        _ functionName: String = #function,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.enter(functionName, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 打印函数退出
    func exit(
        _ functionName: String = #function,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        let message = "← 退出函数: \(functionName)"
        log(level: .verbose, message: message, prefix: prefix, file: file, function: function, line: line)
    }
    
    /// 静态打印函数退出
    static func exit(
        _ functionName: String = #function,
        prefix: String? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        shared.exit(functionName, prefix: prefix, file: file, function: function, line: line)
    }
    
    // MARK: - 配置方法
    /// 设置日志配置
    static func configure(
        isEnabled: Bool? = nil,
        minimumLogLevel: LogLevel? = nil,
        showTimestamp: Bool? = nil,
        showFileInfo: Bool? = nil,
        showFunctionName: Bool? = nil,
        globalPrefix: String? = nil,
        showGlobalPrefix: Bool? = nil
    ) {
        if let isEnabled = isEnabled {
            self.isEnabled = isEnabled
        }
        if let minimumLogLevel = minimumLogLevel {
            self.minimumLogLevel = minimumLogLevel
        }
        if let showTimestamp = showTimestamp {
            self.showTimestamp = showTimestamp
        }
        if let showFileInfo = showFileInfo {
            self.showFileInfo = showFileInfo
        }
        if let showFunctionName = showFunctionName {
            self.showFunctionName = showFunctionName
        }
        if let globalPrefix = globalPrefix {
            self.globalPrefix = globalPrefix
        }
        if let showGlobalPrefix = showGlobalPrefix {
            self.showGlobalPrefix = showGlobalPrefix
        }
    }
    
    /// 打印当前配置
    static func printConfiguration() {
        separator("Logger Configuration")
        info("是否启用: \(isEnabled)")
        info("最低日志级别: \(minimumLogLevel.rawValue)")
        info("显示时间戳: \(showTimestamp)")
        info("显示文件信息: \(showFileInfo)")
        info("显示函数名: \(showFunctionName)")
        info("全局前缀: \(globalPrefix)")
        info("显示全局前缀: \(showGlobalPrefix)")
        separator()
    }
}

// MARK: - 预定义业务Logger实例
extension Logger {
    /// 数据库相关日志
    static let database = Logger(businessPrefix: "Database")
    
    /// 网络相关日志
    static let network = Logger(businessPrefix: "Network")
    
    /// UI相关日志
    static let ui = Logger(businessPrefix: "UI")
    
    /// 设置相关日志
    static let settings = Logger(businessPrefix: "Settings")
    
    /// 认证相关日志
    static let auth = Logger(businessPrefix: "Auth")
    
    /// 存储相关日志
    static let storage = Logger(businessPrefix: "Storage")
    
    /// 统计相关日志
    static let analytics = Logger(businessPrefix: "Analytics")
}

// MARK: - 便捷全局函数
#if DEBUG
/// 全局Debug日志函数
func logDebug(
    _ message: String,
    prefix: String? = nil,
    file: String = #file,
    function: String = #function,
    line: Int = #line
) {
    Logger.debug(message, prefix: prefix, file: file, function: function, line: line)
}

/// 全局Info日志函数
func logInfo(
    _ message: String,
    prefix: String? = nil,
    file: String = #file,
    function: String = #function,
    line: Int = #line
) {
    Logger.info(message, prefix: prefix, file: file, function: function, line: line)
}

/// 全局Warning日志函数
func logWarning(
    _ message: String,
    prefix: String? = nil,
    file: String = #file,
    function: String = #function,
    line: Int = #line
) {
    Logger.warning(message, prefix: prefix, file: file, function: function, line: line)
}

/// 全局Error日志函数
func logError(
    _ message: String,
    prefix: String? = nil,
    file: String = #file,
    function: String = #function,
    line: Int = #line
) {
    Logger.error(message, prefix: prefix, file: file, function: function, line: line)
}
#endif 
