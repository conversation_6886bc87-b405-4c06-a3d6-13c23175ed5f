//
//  ComponentType.swift
//  magicBox
//
//  Created by xmly on 2025/7/8.
//

import Foundation

/// 组件类型枚举，用于管理所有组件的ID
enum ComponentType {
    /// 颜色组件
    case color
    /// 文字组件
    case text
    
    /// 获取组件ID
    var componentId: Int64 {
        switch self {
        case .color:
            return 999
        case .text:
            return 998
        }
    }
    
    /// 获取组件名称（用于日志或调试）
    var name: String {
        switch self {
        case .color:
            return "Color"
        case .text:
            return "Text"
        }
    }
    
    /// 获取组件本地化标题
    var localizedTitle: String {
        switch self {
        case .color:
            return NSLocalizedString("component.color.title", comment: "Colors")
        case .text:
            return NSLocalizedString("component.text.title", comment: "Texts")
        }
    }
} 