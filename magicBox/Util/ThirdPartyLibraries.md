# 第三方库使用说明

本文档总结了项目中使用的第三方库及其用途。所有依赖都通过 Swift Package Manager (SPM) 进行管理。

## UI 相关

### 1. SnapKit
**状态**: ✅ 已导入且正在使用
**使用位置**: 
- Base/BaseViewController.swift
- HomeViewController.swift
- Setting/Options/OptionsViewController.swift
- Setting/Options/ColorsViewController.swift
- Statistics/相关视图
**用途**: 
- 用于简化Auto Layout约束的编写
- 提供了更直观和链式的语法来设置UI布局
- 在项目中主要用于各个视图控制器和自定义视图的布局设置

### 2. FloatingPanel
**状态**: ❌ 已导入但未使用
**用途**: 
- 提供类似Apple Maps的浮动面板UI组件
- 支持拖拽手势和多个停靠位置

### 3. IQKeyboardManagerSwift
**状态**: ✅
**用途**: 
- 自动处理键盘显示/隐藏
- 防止键盘遮挡输入框
- 提供键盘工具栏导航

### 4. Popovers
**状态**: ❌ 已导入但未使用
**用途**: 
- 提供弹出式菜单和提示框
- 支持自定义样式和动画

## 图片处理

### 1. ZLPhotoBrowser
**状态**: ✅ 已导入且正在使用
**使用位置**: Util/ImagePickerManager.swift
**用途**: 
- 图片/视频选择器
- 支持多选、预览、编辑等功能

### 2. HXPhotoPicker
**状态**: ❌ 已导入但未使用
**用途**: 
- 图片选择和编辑
- 支持自定义相机

### 3. SKPhotoBrowser
**状态**: ❌ 已导入但未使用
**用途**: 
- 图片浏览器
- 支持缩放、分享等功能

### 4. Mantis
**状态**: ❌ 已导入但未使用
**用途**: 
- 图片裁剪工具
- 支持多种裁剪比例和手势操作

### 5. OpenCV
**状态**: ❌ 已导入但未使用
**用途**: 
- 计算机视觉处理库
- 图像处理和分析

## 数据存储

### 1. SQLite.swift
**状态**: ✅ 已导入且正在使用
**使用位置**: 
- Sqlite/Models/DatabaseSchema.swift
- Sqlite/Database/DatabaseManager.swift
- Sqlite/Database/TableCreation.swift
- Sqlite/Database/Operations/各种数据库操作文件
**用途**: 
- SQLite数据库的Swift封装
- 提供类型安全的数据库操作API

### 2. ZipArchive
**状态**: ❌ 已导入但未使用
**用途**: 
- ZIP文件的压缩和解压缩
- 支持密码保护

### 3. Files
**状态**: ❌ 已导入但未使用
**用途**: 
- 文件系统操作的简单API
- 支持文件和目录的创建、移动、复制等操作

## 权限管理

### PermissionsKit 系列
**状态**: 部分使用
- PhotoLibraryPermission: ✅ 已导入且正在使用（在 ImagePickerManager.swift）
- CameraPermission: ❌ 已导入但未使用
- FaceIDPermission: ❌ 已导入但未使用
- NotificationPermission: ❌ 已导入但未使用

## 工具库

### 1. Device
**状态**: ❌ 已导入但未使用
**用途**: 
- 设备信息检测
- 提供设备型号、系统版本等信息

### 2. SFSafeSymbols
**状态**: ❌ 已导入但未使用
**用途**: 
- SF Symbols的类型安全访问
- 提供编译时符号名称检查

### 3. Tiercel
**状态**: ❌ 已导入但未使用
**用途**: 
- 下载管理器
- 支持断点续传、后台下载等功能

### 4. StoreHelper
**状态**: ❌ 已导入但未使用
**用途**: 
- App内购买功能的封装
- 简化StoreKit的使用

### 5. HorizonCalendar
**状态**: ❌ 已导入但未使用
**用途**: 
- 自定义日历控件
- 支持多种日历样式和交互

### 6. CountryPicker
**状态**: ❌ 已导入但未使用
**用途**: 
- 国家/地区选择器
- 支持搜索和本地化

### 7. LookinServer
**状态**: ❌ 已导入但未使用
**用途**: 
- UI调试工具
- 仅在开发环境使用

## 注意事项

1. 目前实际使用的库只有：
   - SnapKit（UI布局）
   - SQLite.swift（数据库操作）
   - ZLPhotoBrowser（图片选择）
   - PhotoLibraryPermission（照片库权限）
2. 其他已导入但未使用的库可以考虑移除，以减小应用体积
3. 在添加新功能时，优先考虑使用已导入的库，避免重复导入类似功能的库

## 版本控制

建议在更新依赖时：
1. 使用明确的版本号而不是分支名
2. 在更新前查看更新日志
3. 在测试环境充分测试后再更新生产环境 
