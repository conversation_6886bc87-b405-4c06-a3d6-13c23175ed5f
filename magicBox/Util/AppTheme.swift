import UIKit

/// 应用主题管理器
public enum AppTheme {
    
    /// 颜色相关的常量和工具方法
    public enum Colors {
        // MARK: - 主题色
        /// 主色调
        public static var primary: UIColor {
            return UIColor.systemBlue
        }
        
        /// 次要色调
        public static var secondary: UIColor {
            return UIColor.systemIndigo
        }
        
        /// 强调色
        public static var accent: UIColor {
            return UIColor.systemOrange
        }
        
        // MARK: - 背景色
        /// 主背景色
        public static var background: UIColor {
            return UIColor.systemBackground
        }
        
        /// 分组背景色
        public static var groupedBackground: UIColor {
            return UIColor.systemGroupedBackground
        }
        
        /// 次要分组背景色
        public static var secondaryGroupedBackground: UIColor {
            return UIColor.secondarySystemGroupedBackground
        }
        
        /// 三级分组背景色
        public static var tertiaryGroupedBackground: UIColor {
            return UIColor.tertiarySystemGroupedBackground
        }
        
        // MARK: - 文本色
        /// 主文本色
        public static var label: UIColor {
            return UIColor.label
        }
        
        /// 次要文本色
        public static var secondaryLabel: UIColor {
            return UIColor.secondaryLabel
        }
        
        /// 三级文本色
        public static var tertiaryLabel: UIColor {
            return UIColor.tertiaryLabel
        }
        
        // MARK: - 分割线
        /// 分割线颜色
        public static var separator: UIColor {
            return UIColor.separator
        }
        
        // MARK: - 状态色
        /// 成功状态色
        public static var success: UIColor {
            return UIColor.systemGreen
        }
        
        /// 警告状态色
        public static var warning: UIColor {
            return UIColor.systemYellow
        }
        
        /// 错误状态色
        public static var error: UIColor {
            return UIColor.systemRed
        }
        
        /// 信息提示色
        public static var info: UIColor {
            return UIColor.systemBlue
        }
        
        // MARK: - 按钮
        /// 主按钮背景色
        public static var primaryButtonBackground: UIColor {
            return primary
        }
        
        /// 主按钮文字色
        public static var primaryButtonText: UIColor {
            return UIColor.white
        }
        
        /// 次要按钮背景色
        public static var secondaryButtonBackground: UIColor {
            return UIColor.tertiarySystemFill
        }
        
        /// 次要按钮文字色
        public static var secondaryButtonText: UIColor {
            return primary
        }
        
        // MARK: - 卡片
        /// 卡片边框色
        public static var cardBorder: UIColor {
            return UIColor.systemGray4
        }
        
        /// 卡片阴影色
        public static var cardShadow: UIColor {
            return UIColor.black.withAlphaComponent(0.1)
        }
    }
    
    /// 获取十六进制颜色
    /// - Parameter hex: 十六进制颜色字符串，例如 "#FF0000"
    /// - Returns: UIColor 实例
    public static func color(fromHex hex: String) -> UIColor? {
        var hexSanitized = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        hexSanitized = hexSanitized.replacingOccurrences(of: "#", with: "")
        
        var rgb: UInt64 = 0
        
        guard Scanner(string: hexSanitized).scanHexInt64(&rgb) else {
            return nil
        }
        
        let red = CGFloat((rgb & 0xFF0000) >> 16) / 255.0
        let green = CGFloat((rgb & 0x00FF00) >> 8) / 255.0
        let blue = CGFloat(rgb & 0x0000FF) / 255.0
        
        return UIColor(red: red, green: green, blue: blue, alpha: 1.0)
    }
    
    /// 获取颜色的十六进制字符串
    /// - Parameter color: UIColor 实例
    /// - Returns: 十六进制颜色字符串，例如 "#FF0000"
    public static func hexString(from color: UIColor) -> String {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        let rgb = Int(red * 255) << 16 | Int(green * 255) << 8 | Int(blue * 255) << 0
        
        return String(format: "#%06x", rgb)
    }
}

// MARK: - 便捷扩展
public extension UIColor {
    /// 从十六进制字符串创建颜色
    convenience init?(hex: String) {
        guard let color = AppTheme.color(fromHex: hex) else {
            return nil
        }
        self.init(cgColor: color.cgColor)
    }
    
    /// 获取颜色的十六进制字符串
    var hexString: String {
        return AppTheme.hexString(from: self)
    }
} 