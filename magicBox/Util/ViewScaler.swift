import UIKit

/// 视图尺寸缩放工具类
class ViewScaler {
    /// 基准屏幕宽度 (iPhone 8 Plus/iPhone XR/iPhone XS Max etc.)
    private static let baseScreenWidth: CGFloat = 414.0
    
    /// 当前设备的屏幕宽度
    private static var screenWidth: CGFloat {
        return UIScreen.main.bounds.width
    }
    
    /// 判断当前设备是否为 iPhone
    private static var isIPhone: Bool {
        return UIDevice.current.userInterfaceIdiom == .phone
    }
    
    /// 获取缩放后的尺寸
    /// - Parameter value: 原始尺寸值
    /// - Returns: 根据设备类型和屏幕尺寸计算后的值
    static func scaled(_ value: CGFloat) -> CGFloat {
        // 如果不是 iPhone，直接返回原始值
        guard isIPhone else {
            return value
        }
        
        // 如果是 iPhone 且屏幕宽度大于等于基准宽度，直接返回原始值
        guard screenWidth < baseScreenWidth else {
            return value
        }
        
        // 计算缩放比例并返回缩放后的值
        let scale = screenWidth / baseScreenWidth
        return value * scale
    }
    
    /// 获取缩放后的尺寸（Int 类型）
    /// - Parameter value: 原始尺寸值
    /// - Returns: 根据设备类型和屏幕尺寸计算后的值
    static func scaled(_ value: Int) -> Int {
        return Int(scaled(CGFloat(value)))
    }
    
    /// 获取缩放后的尺寸（Double 类型）
    /// - Parameter value: 原始尺寸值
    /// - Returns: 根据设备类型和屏幕尺寸计算后的值
    static func scaled(_ value: Double) -> Double {
        return Double(scaled(CGFloat(value)))
    }
} 