//
//  StatisticsModels.swift
//  magicBox
//
//  Created by xmly on 2025/7/8.
//

import Foundation
import SwiftUI

// MARK: - 数据模型
struct MonthlyData: Identifiable {
    let id = UUID()
    let month: String
    let count: Int
    
    init(month: String, count: Int) {
        self.month = month
        self.count = count
    }
}

struct CategoryData: Identifiable {
    let id = UUID()
    let category: String
    let count: Int
    let color: Color
    
    init(category: String, count: Int, color: Color) {
        self.category = category
        self.count = count
        self.color = color
    }
} 