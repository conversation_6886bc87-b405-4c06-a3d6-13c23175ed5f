import Foundation
import UIKit

/// 统计模块的常量定义
struct StatisticsConstants {
    struct Layout {
        // MARK: - Controller Layout (StatisticsViewController)
        /// TableView 的内容边距
        static let contentInset: CGFloat = ViewScaler.scaled(16)
        
        // MARK: - Chart Layout (MonthlyChartCell & CategoryChartCell)
        /// 折线图内容高度
        static let lineChartContentHeight: CGFloat = ViewScaler.scaled(230)
        /// 饼图内容高度
        static let pieChartContentHeight: CGFloat = ViewScaler.scaled(230)
        /// 图表顶部边距
        static let chartTopPadding: CGFloat = ViewScaler.scaled(20)
        /// 图表底部边距
        static let chartBottomPadding: CGFloat = ViewScaler.scaled(10)
        /// 图表左右边距
        static let chartSidePadding: CGFloat = ViewScaler.scaled(10)
        /// 折线图总高度（内容高度 + 上下边距）
        static let lineChartHeight: CGFloat = ViewScaler.scaled(lineChartContentHeight + chartTopPadding + chartBottomPadding)
        /// 饼图总高度（内容高度 + 上下边距）
        static let pieChartHeight: CGFloat = ViewScaler.scaled(pieChartContentHeight + chartBottomPadding)
        /// 饼图内圆比例
        static let pieChartInnerCircleRatio: CGFloat = 0.618 // 比例值不需要缩放
        
        // MARK: - Header Layout (StatisticsHeaderView)
        /// 标题文字大小
        static let titleFontSize: CGFloat = ViewScaler.scaled(20)
        /// 标题栏高度
        static let headerHeight: CGFloat = ViewScaler.scaled(44)
        
        // MARK: - Spacing
        /// 分区间距
        static let sectionSpacing: CGFloat = ViewScaler.scaled(30)
        /// 标题间距
        static let titleSpacing: CGFloat = ViewScaler.scaled(10)
        
        // MARK: - 图例布局常量
        /// 图例项的高度
        static let legendItemHeight: CGFloat = ViewScaler.scaled(20)
        /// 图例项之间的间距
        static let legendItemSpacing: CGFloat = ViewScaler.scaled(8)
        /// 图例项的内边距
        static let legendItemPadding: CGFloat = ViewScaler.scaled(8)
        /// 图例整体的垂直内边距
        static let legendVerticalPadding: CGFloat = ViewScaler.scaled(16)
        /// 图例颜色点的大小
        static let legendDotSize: CGFloat = ViewScaler.scaled(8)
    }
    
    enum Chart {
        /// 折线图线条宽度
        static let lineWidth: CGFloat = ViewScaler.scaled(3)
    }
} 
