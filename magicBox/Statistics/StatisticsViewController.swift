//
//  StatisticsViewController.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/5/18.
//

import UIKit
import SwiftUI
import Charts
import SnapKit

class StatisticsViewController: BaseViewController {
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        table.contentInset = UIEdgeInsets(top: StatisticsConstants.Layout.contentInset, left: 0, 
                                        bottom: StatisticsConstants.Layout.contentInset, right: 0)
        table.backgroundColor = .clear
        
        // 注册 cells
        table.register(LineChartCell.self, forCellReuseIdentifier: LineChartCell.reuseIdentifier)
        table.register(PieChartCell.self, forCellReuseIdentifier: PieChartCell.reuseIdentifier)
        
        return table
    }()
    
    // MARK: - 示例数据
    private let monthlyData: [MonthlyData] = [
        MonthlyData(month: "1月", count: 5),
        MonthlyData(month: "2月", count: 8),
        MonthlyData(month: "3月", count: 12),
        MonthlyData(month: "4月", count: 7),
        MonthlyData(month: "5月", count: 15),
        MonthlyData(month: "6月", count: 10),
        MonthlyData(month: "7月", count: 13),
        MonthlyData(month: "8月", count: 18),
        MonthlyData(month: "9月", count: 11),
        MonthlyData(month: "10月", count: 14),
        MonthlyData(month: "11月", count: 9),
        MonthlyData(month: "12月", count: 16)
    ]
    
    private let categoryData: [CategoryData] = [
        CategoryData(category: "工作", count: 30, color: .blue),
        CategoryData(category: "学习", count: 25, color: .green),
        CategoryData(category: "娱乐", count: 20, color: .orange),
        CategoryData(category: "运动", count: 15, color: .red),
        CategoryData(category: "阅读", count: 12, color: .purple),
        CategoryData(category: "社交", count: 18, color: .pink),
        CategoryData(category: "购物", count: 8, color: .mint),
        CategoryData(category: "旅行", count: 6, color: .teal),
        CategoryData(category: "美食", count: 10, color: .brown),
        CategoryData(category: "休息", count: 22, color: .indigo),
        CategoryData(category: "家务", count: 9, color: .cyan),
        CategoryData(category: "写作", count: 7, color: .gray)
    ]
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = AppTheme.Colors.groupedBackground
        title = NSLocalizedString("statistics.title", comment: "Statistics")
        
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }
}

// MARK: - UITableViewDataSource
extension StatisticsViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 2
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
        case 0:
            let cell = tableView.dequeueReusableCell(withIdentifier: LineChartCell.reuseIdentifier, for: indexPath) as! LineChartCell
            cell.configure(with: monthlyData)
            return cell
            
        case 1:
            let cell = tableView.dequeueReusableCell(withIdentifier: PieChartCell.reuseIdentifier, for: indexPath) as! PieChartCell
            cell.configure(with: categoryData)
            return cell
            
        default:
            return UITableViewCell()
        }
    }
}

// MARK: - UITableViewDelegate
extension StatisticsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch indexPath.section {
        case 0:
            return LineChartCell.calculateHeight(with: monthlyData)
        case 1:
            return PieChartCell.calculateHeight(with: categoryData)
        default:
            return 0
        }
    }
}

 
