import UIKit
import SnapKit

class BaseViewController: UIViewController {
    
    // MARK: - Properties
    private var floatingButton: UIButton!
    private let buttonSize: CGFloat = 56
    private let buttonMargin: CGFloat = 20
    
    /// 控制浮动按钮的显示和隐藏
    public var showsFloatingButton: Bool = false {
        didSet {
            floatingButton?.isHidden = !showsFloatingButton
        }
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupBaseUI()
        setupFloatingButton()
    }
    
    // MARK: - View Controller Lifecycle
    override func viewSafeAreaInsetsDidChange() {
        super.viewSafeAreaInsetsDidChange()
        updateFloatingButtonConstraints()
    }
    
    // MARK: - Actions
    @objc private func floatingButtonTapped() {
        // 子类重写此方法来处理按钮点击事件
        handleFloatingButtonTap()
    }
    
    /// 子类重写此方法来处理浮动按钮的点击事件
    @objc open func handleFloatingButtonTap() {
        // 默认实现为空，子类重写此方法来处理点击事件
    }
    
    // MARK: - Helper Methods
    /// 显示一个简单的警告对话框
    /// - Parameters:
    ///   - title: 标题
    ///   - message: 消息内容
    ///   - completion: 点击确定后的回调
    func showAlert(title: String, message: String, completion: (() -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.ok", comment: "OK"), style: .default) { _ in
            completion?()
        })
        present(alert, animated: true)
    }
    
    /// 显示加载指示器
    /// - Parameter message: 加载时显示的消息
    func showLoading(message: String = NSLocalizedString("common.loading", comment: "Loading...")) {
        // TODO: 实现加载指示器
    }
    
    /// 隐藏加载指示器
    func hideLoading() {
        // TODO: 实现隐藏加载指示器
    }
}

// MARK: - UI Setup
extension BaseViewController {
    private func setupBaseUI() {
        // 设置通用的背景色
        view.backgroundColor = AppTheme.Colors.groupedBackground
        
        // 设置导航栏大标题
        navigationController?.navigationBar.prefersLargeTitles = true
    }
    
    private func setupFloatingButton() {
        // 创建按钮
        floatingButton = UIButton(type: .system)
        
        // 设置按钮样式
        floatingButton.backgroundColor = AppTheme.Colors.primary
        floatingButton.layer.cornerRadius = buttonSize / 2
        
        // 添加阴影效果
        floatingButton.layer.shadowColor = UIColor.black.cgColor
        floatingButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        floatingButton.layer.shadowRadius = 4
        floatingButton.layer.shadowOpacity = 0.2
        
        // 设置加号图标
        let config = UIImage.SymbolConfiguration(pointSize: 24, weight: .medium)
        let plusImage = UIImage(systemName: "plus", withConfiguration: config)
        floatingButton.setImage(plusImage, for: .normal)
        floatingButton.tintColor = .white
        
        // 添加到视图
        view.addSubview(floatingButton)
        
        // 默认隐藏
        floatingButton.isHidden = !showsFloatingButton
        
        // 添加点击事件
        floatingButton.addTarget(self, action: #selector(floatingButtonTapped), for: .touchUpInside)
        
        // 设置约束
        updateFloatingButtonConstraints()
    }
    
    private func updateFloatingButtonConstraints() {
        guard let _ = floatingButton.superview else { return }
        
        floatingButton.snp.remakeConstraints { make in
            make.size.equalTo(buttonSize)
            make.trailing.equalTo(view.safeAreaLayoutGuide).offset(-buttonMargin)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-buttonMargin)
        }
        view.bringSubviewToFront(floatingButton)
    }
} 

