//
//  TabBarAwareNavigationController.swift
//  magicBox
//
//  Created by xmly on 2025/1/8.
//

import UIKit

/// 自动处理TabBar显示隐藏的导航控制器
class TabBarAwareNavigationController: UINavigationController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }
    
    override func pushViewController(_ viewController: UIViewController, animated: <PERSON><PERSON>) {
        // 如果当前导航栈中只有一个控制器（即根控制器），说明要推送到二级页面
        if viewControllers.count == 1 {
            viewController.hidesBottomBarWhenPushed = true
        }
        
        super.pushViewController(viewController, animated: animated)
    }
} 