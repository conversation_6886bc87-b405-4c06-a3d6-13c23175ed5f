//
//  OptionsViewController.swift
//  magicBox
//
//  Created by xmly on 2025/7/8.
//

import UIKit
import SnapKit

class OptionsViewController: BaseViewController {
    // MARK: - UI组件
    private var tableView: UITableView!
    
    // MARK: - 数据
    private struct OptionSection {
        let title: String
        let items: [OptionItem]
    }
    
    private struct OptionItem {
        let title: String
        let subtitle: String
        let icon: String
        let action: () -> Void
    }
    
    private var sections: [OptionSection] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        setupData()
    }
    
    // MARK: - UI设置
    private func setupUI() {
        
        // 设置TableView
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "OptionCell")
        tableView.backgroundColor = AppTheme.Colors.groupedBackground
        view.addSubview(tableView)
        
        // 使用SnapKit设置约束
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = NSLocalizedString("options.title", comment: "Options Management")
    }
    
    private func setupData() {
        sections = [
            OptionSection(title: NSLocalizedString("options.appearance.title", comment: "Appearance"), items: [
                OptionItem(
                    title: NSLocalizedString("options.colors.title", comment: "Colors"),
                    subtitle: NSLocalizedString("options.colors.subtitle", comment: "Manage color options"),
                    icon: "paintpalette.fill",
                    action: { [weak self] in
                        self?.showColorOptions()
                    }
                ),
                OptionItem(
                    title: NSLocalizedString("options.texts.title", comment: "Texts"),
                    subtitle: NSLocalizedString("options.texts.subtitle", comment: "Manage text options"),
                    icon: "text.alignleft",
                    action: { [weak self] in
                        self?.showTextOptions()
                    }
                )
                // 在这里可以添加更多外观相关的选项，如字体、主题等
            ])
            // 在这里可以添加更多选项分类，如尺寸、材质等
        ]
    }
    
    // MARK: - 导航
    private func showColorOptions() {
        let colorVC = ColorsViewController()
        navigationController?.pushViewController(colorVC, animated: true)
    }
    
    private func showTextOptions() {
        let textVC = TextOptionsViewController()
        navigationController?.pushViewController(textVC, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension OptionsViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sections[section].items.count
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return sections[section].title
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "OptionCell", for: indexPath)
        let item = sections[indexPath.section].items[indexPath.row]
        
        var config = cell.defaultContentConfiguration()
        config.text = item.title
        config.secondaryText = item.subtitle
        config.image = UIImage(systemName: item.icon)
        
        cell.contentConfiguration = config
        cell.accessoryType = .disclosureIndicator
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension OptionsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let item = sections[indexPath.section].items[indexPath.row]
        item.action()
    }
} 
