//
//  TextOptionsViewController.swift
//  magicBox
//
//  Created by xmly on 2025/7/8.
//

import UIKit
import SnapKit

class TextOptionsViewController: BaseViewController {
    // MARK: - UI组件
    private var tableView: UITableView!
    private var addButton: UIButton!
    
    // MARK: - 数据
    private var textOptions: [ComponentOption] = []
    private let textComponentId: Int64 = ComponentType.text.componentId
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        loadTextOptions()
    }
    
    // MARK: - UI设置
    private func setupUI() {
        // 设置TableView
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(TextOptionTableViewCell.self, forCellReuseIdentifier: "TextOptionCell")
        tableView.rowHeight = 60
        tableView.backgroundColor = AppTheme.Colors.groupedBackground
        view.addSubview(tableView)
        
        // 设置添加按钮
        addButton = UIButton(type: .system)
        addButton.setTitle(NSLocalizedString("text_options.add_text", comment: "Add Text"), for: .normal)
        addButton.backgroundColor = AppTheme.Colors.primaryButtonBackground
        addButton.setTitleColor(AppTheme.Colors.primaryButtonText, for: .normal)
        addButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        addButton.layer.cornerRadius = 12
        addButton.addTarget(self, action: #selector(addTextTapped), for: .touchUpInside)
        view.addSubview(addButton)
        
        // 使用SnapKit设置约束
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(addButton.snp.top).offset(-16)
        }
        
        addButton.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.height.equalTo(50)
        }
    }
    
    private func setupNavigationBar() {
        title = NSLocalizedString("text_options.title", comment: "Text Management")
        
        // 添加编辑按钮
        navigationItem.rightBarButtonItem = editButtonItem
    }
    
    // MARK: - 数据加载
    func loadTextOptions() {
        do {
            // 使用DataSeeder初始化默认文字数据
            try DataSeeder.shared.initializeTextOptions()
            
            // 加载文字选项
            textOptions = try DatabaseManager.shared.getComponentOptions(componentId: textComponentId)
            
            tableView.reloadData()
        } catch {
            print("加载文字选项失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("text_options.load_error", comment: "Failed to load text data"))
        }
    }
    
    // MARK: - 按钮事件
    @objc private func addTextTapped() {
        showAddTextDialog()
    }
    
    // MARK: - 辅助方法
    private func showAddTextDialog() {
        let alert = UIAlertController(title: NSLocalizedString("text_options.add_text_title", comment: "Add New Text"), message: NSLocalizedString("text_options.add_text_message", comment: "Please enter text information"), preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = NSLocalizedString("text_options.text_display_placeholder", comment: "Display Text")
        }
        
        alert.addTextField { textField in
            textField.placeholder = NSLocalizedString("text_options.text_value_placeholder", comment: "Value")
        }
        
        let addAction = UIAlertAction(title: NSLocalizedString("common.add", comment: "Add"), style: .default) { [weak self] _ in
            guard let self = self,
                  let displayField = alert.textFields?[0],
                  let valueField = alert.textFields?[1],
                  let display = displayField.text, !display.isEmpty,
                  let value = valueField.text, !value.isEmpty else {
                return
            }
            
            self.addText(display: display, value: value)
        }
        
        let cancelAction = UIAlertAction(title: NSLocalizedString("common.cancel", comment: "Cancel"), style: .cancel)
        
        alert.addAction(addAction)
        alert.addAction(cancelAction)
        
        present(alert, animated: true)
    }
    
    private func addText(display: String, value: String) {
        do {
            let option = ComponentOption(
                componentId: textComponentId,
                optionValue: value,
                optionDisplay: display,
                sortOrder: textOptions.count
            )
            _ = try DatabaseManager.shared.createComponentOption(option)
            loadTextOptions()
        } catch {
            print("添加文字选项失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("text_options.add_error", comment: "Failed to add text"))
        }
    }
    
    // MARK: - 编辑模式
    override func setEditing(_ editing: Bool, animated: Bool) {
        super.setEditing(editing, animated: animated)
        tableView.setEditing(editing, animated: animated)
    }
}

// MARK: - TableView DataSource & Delegate
extension TextOptionsViewController: UITableViewDataSource, UITableViewDelegate {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return textOptions.count
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return NSLocalizedString("text_options.section.title", comment: "Text Options")
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "TextOptionCell", for: indexPath) as! TextOptionTableViewCell
        let option = textOptions[indexPath.row]
        cell.configure(with: option)
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let option = textOptions[indexPath.row]
        showEditTextDialog(for: option, at: indexPath)
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            deleteText(at: indexPath)
        }
    }
    
    // 添加移动功能
    func tableView(_ tableView: UITableView, canMoveRowAt indexPath: IndexPath) -> Bool {
        return isEditing
    }
    
    func tableView(_ tableView: UITableView, moveRowAt sourceIndexPath: IndexPath, to destinationIndexPath: IndexPath) {
        // 更新数据源
        let movedOption = textOptions.remove(at: sourceIndexPath.row)
        textOptions.insert(movedOption, at: destinationIndexPath.row)
        
        // 更新数据库中的排序顺序
        updateTextSortOrder()
    }
    
    private func updateTextSortOrder() {
        do {
            // 更新每个选项的排序顺序
            for (index, option) in textOptions.enumerated() {
                if let optionId = option.id {
                    let updatedOption = ComponentOption(
                        id: optionId,
                        componentId: option.componentId,
                        optionValue: option.optionValue,
                        optionDisplay: option.optionDisplay,
                        metadata: option.metadata,
                        isActive: option.isActive,
                        sortOrder: index,
                        createdAt: option.createdAt
                    )
                    try DatabaseManager.shared.updateComponentOption(id: optionId, option: updatedOption)
                }
            }
            
            // 重新加载数据以确保显示正确的顺序
            loadTextOptions()
        } catch {
            print("更新文字排序失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("text_options.update_error", comment: "Failed to update text"))
        }
    }
    
    private func showEditTextDialog(for option: ComponentOption, at indexPath: IndexPath) {
        let alert = UIAlertController(title: NSLocalizedString("text_options.edit_text_title", comment: "Edit Text"), message: NSLocalizedString("text_options.edit_text_message", comment: "Modify text information"), preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.text = option.optionDisplay
            textField.placeholder = NSLocalizedString("text_options.text_display_placeholder", comment: "Display Text")
        }
        
        alert.addTextField { textField in
            textField.text = option.optionValue
            textField.placeholder = NSLocalizedString("text_options.text_value_placeholder", comment: "Value")
        }
        
        let saveAction = UIAlertAction(title: NSLocalizedString("common.save", comment: "Save"), style: .default) { [weak self] _ in
            guard let self = self,
                  let displayField = alert.textFields?[0],
                  let valueField = alert.textFields?[1],
                  let display = displayField.text, !display.isEmpty,
                  let value = valueField.text, !value.isEmpty,
                  let optionId = option.id else {
                return
            }
            
            self.updateText(display: display, value: value, optionId: optionId)
        }
        
        let cancelAction = UIAlertAction(title: NSLocalizedString("common.cancel", comment: "Cancel"), style: .cancel)
        
        alert.addAction(saveAction)
        alert.addAction(cancelAction)
        
        present(alert, animated: true)
    }
}

// MARK: - 数据库相关操作
extension TextOptionsViewController {
    // 删除文字选项
    func deleteText(at indexPath: IndexPath) {
        let option = textOptions[indexPath.row]
        do {
            if let optionId = option.id {
                _ = try DatabaseManager.shared.deleteComponentOption(id: optionId)
                textOptions.remove(at: indexPath.row)
                tableView.deleteRows(at: [indexPath], with: .fade)
            }
        } catch {
            print("删除文字选项失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("text_options.delete_error", comment: "Failed to delete text"))
        }
    }
    
    // 更新文字选项
    func updateText(display: String, value: String, optionId: Int64) {
        do {
            let option = ComponentOption(
                id: optionId,
                componentId: textComponentId,
                optionValue: value,
                optionDisplay: display,
                metadata: nil,
                isActive: true,
                sortOrder: textOptions.first { $0.id == optionId }?.sortOrder ?? 0,
                createdAt: Date()
            )
            _ = try DatabaseManager.shared.updateComponentOption(id: optionId, option: option)
            loadTextOptions()
        } catch {
            print("更新文字选项失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("text_options.update_error", comment: "Failed to update text"))
        }
    }
}

// MARK: - 自定义Cell
class TextOptionTableViewCell: UITableViewCell {
    private let optionLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = AppTheme.Colors.label
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        contentView.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        
        contentView.addSubview(optionLabel)
        
        // 使用SnapKit设置约束
        optionLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
    }
    
    override func setHighlighted(_ highlighted: Bool, animated: Bool) {
        super.setHighlighted(highlighted, animated: animated)
        let newBgColor: UIColor = highlighted ? AppTheme.Colors.tertiaryGroupedBackground : AppTheme.Colors.secondaryGroupedBackground
        UIView.animate(withDuration: 0.2) {
            self.backgroundColor = newBgColor
            self.contentView.backgroundColor = newBgColor
        }
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
        let newBgColor: UIColor = selected ? AppTheme.Colors.tertiaryGroupedBackground : AppTheme.Colors.secondaryGroupedBackground
        UIView.animate(withDuration: 0.2) {
            self.backgroundColor = newBgColor
            self.contentView.backgroundColor = newBgColor
        }
    }
    
    func configure(with option: ComponentOption) {
        optionLabel.text = option.optionDisplay
    }
}

// MARK: - 提示框
fileprivate extension TextOptionsViewController {
    func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.ok", comment: "OK"), style: .default))
        present(alert, animated: true)
    }
} 