import Foundation

// MARK: - 数据预设管理器
/// 专门负责向数据库初始化预设数据，如预定义颜色选项等
/// 注意：日常的颜色管理操作请使用 ColorManager 类
class DataSeeder {
    
    // MARK: - 单例
    static let shared = DataSeeder()
    
    private init() {}
    
    // MARK: - 常量定义
    /// 颜色组件的默认ID
    private let colorComponentId: Int64 = 999
    
    // MARK: - 预定义颜色数据
    /// 预定义的颜色选项
    private let predefinedColors: [(key: String, name: String, value: String)] = [
        ("red", "红色", "#FF0000"),
        ("green", "绿色", "#00FF00"),
        ("blue", "蓝色", "#0000FF"),
        ("yellow", "黄色", "#FFFF00"),
        ("purple", "紫色", "#800080"),
        ("orange", "橙色", "#FFA500"),
        ("pink", "粉色", "#FFC0CB"),
        ("brown", "棕色", "#A52A2A"),
        ("gray", "灰色", "#808080"),
        ("black", "黑色", "#000000"),
        ("white", "白色", "#FFFFFF"),
        ("cyan", "青色", "#00FFFF"),
        ("magenta", "品红", "#FF00FF"),
        ("lime", "酸橙", "#00FF00"),
        ("maroon", "栗色", "#800000"),
        ("navy", "海军蓝", "#000080"),
        ("olive", "橄榄", "#808000"),
        ("silver", "银色", "#C0C0C0"),
        ("teal", "蓝绿", "#008080"),
        ("indigo", "靛蓝", "#4B0082")
    ]
    
    // MARK: - 公开方法
    
    /// 初始化所有预设数据
    /// - Parameter forceReset: 是否强制重置数据
    /// - Throws: 数据库操作错误
    func initializeDefaultData(forceReset: Bool = false) throws {
        try initializeColorOptions(forceReset: forceReset)
        // 这里可以添加其他类型的预设数据初始化
    }
    
    /// 初始化颜色选项
    /// - Parameter forceReset: 是否强制重置颜色数据
    /// - Throws: 数据库操作错误
    func initializeColorOptions(forceReset: Bool = false) throws {
        // 检查是否已经存在颜色数据
        let existingOptions = try DatabaseManager.shared.getComponentOptions(componentId: colorComponentId)
        
        if existingOptions.isEmpty || forceReset {
            // 如果需要重置，先删除现有数据
            if forceReset {
                try clearColorOptions()
            }
            
            // 添加预定义颜色
            try addPredefinedColors()
            
            Logger.database.info("成功初始化颜色预设数据，共添加 \(predefinedColors.count) 个颜色选项")
        } else {
            Logger.database.info("颜色预设数据已存在，跳过初始化")
        }
    }
    
    // 这些方法已迁移到ColorManager类中
    
    // MARK: - 私有方法
    
    /// 添加预定义颜色选项
    /// - Throws: 数据库操作错误
    private func addPredefinedColors() throws {
        for (index, colorData) in predefinedColors.enumerated() {
            let option = ComponentOption(
                componentId: colorComponentId,
                optionValue: colorData.value,
                optionDisplay: colorData.name,
                metadata: createPredefinedColorMetadata(key: colorData.key),
                isActive: true,
                sortOrder: index
            )
            
            do {
                _ = try DatabaseManager.shared.createComponentOption(option)
            } catch {
                Logger.database.info("创建预定义颜色失败: \(colorData.name) - \(error.localizedDescription)")
                throw error
            }
        }
    }
    
    /// 清除现有的颜色选项
    /// - Throws: 数据库操作错误
    private func clearColorOptions() throws {
        let existingOptions = try DatabaseManager.shared.getAllComponentOptions(componentId: colorComponentId)
        
        for option in existingOptions {
            if let optionId = option.id {
                try DatabaseManager.shared.deleteComponentOption(id: optionId)
            }
        }
        
        Logger.database.info("已清除现有颜色选项数据")
    }
    
    /// 创建预定义颜色的元数据
    /// - Parameter key: 颜色键值
    /// - Returns: JSON格式的元数据字符串
    private func createPredefinedColorMetadata(key: String) -> String? {
        let metadata = [
            "key": key,
            "type": "predefined",
            "category": "color"
        ]
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: metadata, options: []),
              let jsonString = String(data: jsonData, encoding: .utf8) else {
            return nil
        }
        
        return jsonString
    }
}

// MARK: - 工具方法
extension DataSeeder {
    
    /// 获取颜色组件ID
    /// - Returns: 颜色组件ID
    func getColorComponentId() -> Int64 {
        return colorComponentId
    }
} 
