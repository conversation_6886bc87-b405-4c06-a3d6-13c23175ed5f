//
//  DataSeeder.swift
//  magicBox
//
//  Created by xmly on 2025/7/8.
//

import Foundation

/// 数据预设管理器
/// 专门负责向数据库添加预设数据，如预定义颜色选项等
class DataSeeder {
    
    // MARK: - 单例
    static let shared = DataSeeder()
    private init() {}
    
    // MARK: - 预定义数据
    /// 预定义的颜色选项
    private let predefinedColors: [(key: String, name: String, value: String)] = [
        ("red", "红色", "#FF0000"),
        ("green", "绿色", "#00FF00"),
        ("blue", "蓝色", "#0000FF"),
        ("yellow", "黄色", "#FFFF00"),
        ("purple", "紫色", "#800080"),
        ("orange", "橙色", "#FFA500"),
        ("pink", "粉色", "#FFC0CB"),
        ("brown", "棕色", "#A52A2A"),
        ("gray", "灰色", "#808080"),
        ("black", "黑色", "#000000"),
        ("white", "白色", "#FFFFFF"),
        ("cyan", "青色", "#00FFFF"),
        ("magenta", "品红", "#FF00FF"),
        ("lime", "酸橙", "#00FF00"),
        ("maroon", "栗色", "#800000"),
        ("navy", "海军蓝", "#000080"),
        ("olive", "橄榄", "#808000"),
        ("silver", "银色", "#C0C0C0"),
        ("teal", "蓝绿", "#008080"),
        ("indigo", "靛蓝", "#4B0082")
    ]
    
    /// 预定义的文字选项
    private let predefinedTexts: [(key: String, display: String, value: String)] = [
        ("text1", "选项一", "option1"),
        ("text2", "选项二", "option2"),
        ("text3", "选项三", "option3"),
        ("text4", "选项四", "option4"),
        ("text5", "选项五", "option5")
    ]
    
    // MARK: - 公开方法
    
    /// 初始化预设数据
    /// - Throws: 数据库操作错误
    func initializeDefaultData() throws {
        // 检查是否已经存在数据
        let existingColorOptions = try DatabaseManager.shared.getComponentOptions(componentId: ComponentType.color.componentId)
        let existingTextOptions = try DatabaseManager.shared.getComponentOptions(componentId: ComponentType.text.componentId)
        
        // 只在没有数据时添加预设数据
        if existingColorOptions.isEmpty {
            try addPredefinedColors()
            Logger.database.info("成功初始化颜色预设数据，共添加 \(predefinedColors.count) 个颜色选项")
        }
        
        if existingTextOptions.isEmpty {
            try addPredefinedTexts()
            Logger.database.info("成功初始化文字预设数据，共添加 \(predefinedTexts.count) 个文字选项")
        }
    }
    
    // MARK: - 私有方法
    
    /// 添加预定义颜色选项
    /// - Throws: 数据库操作错误
    private func addPredefinedColors() throws {
        for (index, colorData) in predefinedColors.enumerated() {
            let option = ComponentOption(
                componentId: ComponentType.color.componentId,
                optionValue: colorData.value,
                optionDisplay: colorData.name,
                metadata: createPredefinedMetadata(key: colorData.key, category: "color"),
                isActive: true,
                sortOrder: index
            )
            
            try DatabaseManager.shared.createComponentOption(option)
        }
    }
    
    /// 添加预定义文字选项
    /// - Throws: 数据库操作错误
    private func addPredefinedTexts() throws {
        for (index, textData) in predefinedTexts.enumerated() {
            let option = ComponentOption(
                componentId: ComponentType.text.componentId,
                optionValue: textData.value,
                optionDisplay: textData.display,
                metadata: createPredefinedMetadata(key: textData.key, category: "text"),
                isActive: true,
                sortOrder: index
            )
            
            try DatabaseManager.shared.createComponentOption(option)
        }
    }
    
    /// 创建预定义选项的元数据
    /// - Parameters:
    ///   - key: 选项键值
    ///   - category: 选项类别
    /// - Returns: JSON格式的元数据字符串
    private func createPredefinedMetadata(key: String, category: String) -> String? {
        let metadata = [
            "key": key,
            "type": "predefined",
            "category": category
        ]
        
        return try? JSONSerialization.data(withJSONObject: metadata)
            .toString()
    }
}
