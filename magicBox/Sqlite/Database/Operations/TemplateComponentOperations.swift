import SQLite
import Foundation

// MARK: - TemplateComponent 相关操作
extension DatabaseManager {
    
    // MARK: - 创建模板组件
    func createTemplateComponent(_ component: TemplateComponent) throws -> Int64 {
        return try safeExecute { db in
            let insert = Tables.templateComponents.insert(
                TemplateComponentColumns.templateId <- component.templateId,
                TemplateComponentColumns.componentName <- component.componentName,
                TemplateComponentColumns.componentTitle <- component.componentTitle,
                TemplateComponentColumns.componentType <- component.componentType,
                TemplateComponentColumns.isRequired <- component.isRequired,
                TemplateComponentColumns.isMultiValue <- component.isMultiValue,
                TemplateComponentColumns.hasPredefinedOptions <- component.hasPredefinedOptions,
                TemplateComponentColumns.allowCustomValue <- component.allowCustomValue,
                TemplateComponentColumns.sortOrder <- component.sortOrder,
                TemplateComponentColumns.defaultValue <- component.defaultValue
            )
            return try db.run(insert)
        }
    }
    
    // MARK: - 获取模板的所有组件
    func getTemplateComponents(templateId: Int64) throws -> [TemplateComponent] {
        return try safeExecute { db in
            var result: [TemplateComponent] = []
            let query = Tables.templateComponents.filter(TemplateComponentColumns.templateId == templateId).order(TemplateComponentColumns.sortOrder)
            for row in try db.prepare(query) {
                result.append(TemplateComponent(
                    id: row[TemplateComponentColumns.id],
                    templateId: row[TemplateComponentColumns.templateId],
                    componentName: row[TemplateComponentColumns.componentName],
                    componentTitle: row[TemplateComponentColumns.componentTitle],
                    componentType: row[TemplateComponentColumns.componentType],
                    isRequired: row[TemplateComponentColumns.isRequired],
                    isMultiValue: row[TemplateComponentColumns.isMultiValue],
                    hasPredefinedOptions: row[TemplateComponentColumns.hasPredefinedOptions],
                    allowCustomValue: row[TemplateComponentColumns.allowCustomValue],
                    sortOrder: row[TemplateComponentColumns.sortOrder],
                    defaultValue: row[TemplateComponentColumns.defaultValue]
                ))
            }
            return result
        }
    }
    
    // MARK: - 根据ID获取组件
    func getTemplateComponent(id: Int64) throws -> TemplateComponent? {
        return try safeExecute { db in
            let query = Tables.templateComponents.filter(TemplateComponentColumns.id == id)
            if let row = try db.pluck(query) {
                return TemplateComponent(
                    id: row[TemplateComponentColumns.id],
                    templateId: row[TemplateComponentColumns.templateId],
                    componentName: row[TemplateComponentColumns.componentName],
                    componentTitle: row[TemplateComponentColumns.componentTitle],
                    componentType: row[TemplateComponentColumns.componentType],
                    isRequired: row[TemplateComponentColumns.isRequired],
                    isMultiValue: row[TemplateComponentColumns.isMultiValue],
                    hasPredefinedOptions: row[TemplateComponentColumns.hasPredefinedOptions],
                    allowCustomValue: row[TemplateComponentColumns.allowCustomValue],
                    sortOrder: row[TemplateComponentColumns.sortOrder],
                    defaultValue: row[TemplateComponentColumns.defaultValue]
                )
            }
            return nil
        }
    }
    
    // MARK: - 更新组件
    func updateTemplateComponent(id: Int64, component: TemplateComponent) throws -> Bool {
        return try safeExecute { db in
            let comp = Tables.templateComponents.filter(TemplateComponentColumns.id == id)
            let update = comp.update(
                TemplateComponentColumns.componentName <- component.componentName,
                TemplateComponentColumns.componentTitle <- component.componentTitle,
                TemplateComponentColumns.componentType <- component.componentType,
                TemplateComponentColumns.isRequired <- component.isRequired,
                TemplateComponentColumns.isMultiValue <- component.isMultiValue,
                TemplateComponentColumns.hasPredefinedOptions <- component.hasPredefinedOptions,
                TemplateComponentColumns.allowCustomValue <- component.allowCustomValue,
                TemplateComponentColumns.sortOrder <- component.sortOrder,
                TemplateComponentColumns.defaultValue <- component.defaultValue
            )
            return try db.run(update) > 0
        }
    }
    
    // MARK: - 删除组件
    func deleteTemplateComponent(id: Int64) throws -> Bool {
        return try safeExecute { db in
            let component = Tables.templateComponents.filter(TemplateComponentColumns.id == id)
            return try db.run(component.delete()) > 0
        }
    }
    
    // MARK: - 检查组件名是否存在
    func componentNameExists(_ name: String, templateId: Int64, excludeId: Int64? = nil) throws -> Bool {
        return try safeExecute { db in
            var query = Tables.templateComponents.filter(TemplateComponentColumns.componentName == name && TemplateComponentColumns.templateId == templateId)
            if let excludeId = excludeId {
                query = query.filter(TemplateComponentColumns.id != excludeId)
            }
            return try db.scalar(query.count) > 0
        }
    }
} 