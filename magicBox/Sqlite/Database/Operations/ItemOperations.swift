import SQLite
import Foundation

// MARK: - Item 相关操作
extension DatabaseManager {
    
    // MARK: - 创建物品
    func createItem(_ item: Item) throws -> Int64 {
        return try safeExecute { db in
            let insert = Tables.items.insert(
                ItemColumns.templateId <- item.templateId,
                ItemColumns.createdAt <- item.createdAt,
                ItemColumns.updatedAt <- item.updatedAt
            )
            return try db.run(insert)
        }
    }
    
    // MARK: - 获取所有物品
    func getAllItems() throws -> [Item] {
        return try safeExecute { db in
            var result: [Item] = []
            for row in try db.prepare(Tables.items.order(ItemColumns.createdAt.desc)) {
                result.append(Item(
                    id: row[ItemColumns.id],
                    templateId: row[ItemColumns.templateId],
                    createdAt: row[ItemColumns.createdAt],
                    updatedAt: row[ItemColumns.updatedAt]
                ))
            }
            return result
        }
    }
    
    // MARK: - 根据模板ID获取物品
    func getItemsByTemplate(templateId: Int64) throws -> [Item] {
        return try safeExecute { db in
            var result: [Item] = []
            let query = Tables.items.filter(ItemColumns.templateId == templateId).order(ItemColumns.createdAt.desc)
            for row in try db.prepare(query) {
                result.append(Item(
                    id: row[ItemColumns.id],
                    templateId: row[ItemColumns.templateId],
                    createdAt: row[ItemColumns.createdAt],
                    updatedAt: row[ItemColumns.updatedAt]
                ))
            }
            return result
        }
    }
    
    // MARK: - 根据ID获取物品
    func getItem(id: Int64) throws -> Item? {
        return try safeExecute { db in
            let query = Tables.items.filter(ItemColumns.id == id)
            if let row = try db.pluck(query) {
                return Item(
                    id: row[ItemColumns.id],
                    templateId: row[ItemColumns.templateId],
                    createdAt: row[ItemColumns.createdAt],
                    updatedAt: row[ItemColumns.updatedAt]
                )
            }
            return nil
        }
    }
    
    // MARK: - 更新物品
    func updateItem(id: Int64) throws -> Bool {
        return try safeExecute { db in
            let item = Tables.items.filter(ItemColumns.id == id)
            let update = item.update(ItemColumns.updatedAt <- Date())
            return try db.run(update) > 0
        }
    }
    
    // MARK: - 删除物品
    func deleteItem(id: Int64) throws -> Bool {
        return try safeExecute { db in
            let item = Tables.items.filter(ItemColumns.id == id)
            return try db.run(item.delete()) > 0
        }
    }
    
    // MARK: - 获取物品的详细信息（包含属性值和选项显示名称）
    func getItemDetails(itemId: Int64) throws -> [(component: TemplateComponent, values: [(value: String, display: String?, color: String?)])] {
        return try safeExecute { db in
            // 获取物品
            guard let item = try getItem(id: itemId) else { return [] }
            
            // 获取模板组件
            let components = try getTemplateComponents(templateId: item.templateId)
            
            // 获取所有组件的属性值（批量查询）
            let attributeValues = try getItemAttributeValues(itemId: itemId)
            
            // 获取所有相关的组件选项（批量查询）
            let componentIds = components.compactMap { $0.id }
            guard !componentIds.isEmpty else { return [] }
            
            let allOptionsQuery = Tables.componentOptions
                .filter(componentIds.contains(ComponentOptionColumns.componentId) && ComponentOptionColumns.isActive == true)
                .order(ComponentOptionColumns.sortOrder)
            
            var allOptionsDict: [Int64: [ComponentOption]] = [:]
            for row in try db.prepare(allOptionsQuery) {
                let option = ComponentOption(
                    id: row[ComponentOptionColumns.id],
                    componentId: row[ComponentOptionColumns.componentId],
                    optionValue: row[ComponentOptionColumns.optionValue],
                    optionDisplay: row[ComponentOptionColumns.optionDisplay],
                    metadata: row[ComponentOptionColumns.metadata],
                    isActive: row[ComponentOptionColumns.isActive],
                    sortOrder: row[ComponentOptionColumns.sortOrder],
                    createdAt: row[ComponentOptionColumns.createdAt]
                )
                
                if allOptionsDict[option.componentId] == nil {
                    allOptionsDict[option.componentId] = []
                }
                allOptionsDict[option.componentId]?.append(option)
            }
            
            // 将属性值按组件ID分组
            let valuesByComponent = Dictionary(grouping: attributeValues) { $0.componentId }
            
            var result: [(component: TemplateComponent, values: [(value: String, display: String?, color: String?)])] = []
            
            for component in components {
                guard let componentId = component.id else {
                    print("Warning: Component has no ID, skipping...")
                    continue
                }
                
                // 获取该组件的属性值
                let values = valuesByComponent[componentId] ?? []
                
                var displayValues: [(value: String, display: String?, color: String?)] = []
                
                for value in values {
                    // 从预先获取的选项中查找匹配的选项
                    var matchedOption: ComponentOption?
                    if component.hasPredefinedOptions {
                        let options = allOptionsDict[componentId] ?? []
                        matchedOption = options.first { $0.optionValue == value.value }
                    }
                    
                    displayValues.append((
                        value: value.value,
                        display: matchedOption?.optionDisplay,
                        color: nil  // 暂时设置为nil，将来需要时可以从metadata中解析
                    ))
                }
                
                result.append((component: component, values: displayValues))
            }
            
            return result
        }
    }
    
    // MARK: - 搜索物品
    func searchItems(keyword: String) throws -> [Item] {
        return try safeExecute { db in
            var result: [Item] = []
            
            // 通过属性值搜索
            let query = Tables.items
                .join(Tables.itemAttributeValues, on: ItemColumns.id == ItemAttributeValueColumns.itemId)
                .filter(ItemAttributeValueColumns.value.like("%\(keyword)%"))
                .select(distinct: Tables.items[ItemColumns.id], Tables.items[ItemColumns.templateId], Tables.items[ItemColumns.createdAt], Tables.items[ItemColumns.updatedAt])
                .order(ItemColumns.createdAt.desc)
            
            for row in try db.prepare(query) {
                result.append(Item(
                    id: row[ItemColumns.id],
                    templateId: row[ItemColumns.templateId],
                    createdAt: row[ItemColumns.createdAt],
                    updatedAt: row[ItemColumns.updatedAt]
                ))
            }
            
            return result
        }
    }
    
    // MARK: - 统计物品数量
    func getItemCount() throws -> Int {
        return try safeExecute { db in
            return try db.scalar(Tables.items.count)
        }
    }
    
    // MARK: - 根据模板统计物品数量
    func getItemCountByTemplate(templateId: Int64) throws -> Int {
        return try safeExecute { db in
            return try db.scalar(Tables.items.filter(ItemColumns.templateId == templateId).count)
        }
    }
} 