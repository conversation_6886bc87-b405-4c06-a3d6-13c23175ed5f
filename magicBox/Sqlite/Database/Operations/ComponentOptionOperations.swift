import SQLite
import Foundation

// MARK: - ComponentOption 相关操作
extension DatabaseManager {
    
    // MARK: - 创建组件选项
    func createComponentOption(_ option: ComponentOption) throws -> Int64 {
        return try safeExecute { db in
            let insert = Tables.componentOptions.insert(
                ComponentOptionColumns.componentId <- option.componentId,
                ComponentOptionColumns.optionValue <- option.optionValue,
                ComponentOptionColumns.optionDisplay <- option.optionDisplay,
                ComponentOptionColumns.metadata <- option.metadata,
                ComponentOptionColumns.isActive <- option.isActive,
                ComponentOptionColumns.sortOrder <- option.sortOrder,
                ComponentOptionColumns.createdAt <- option.createdAt
            )
            return try db.run(insert)
        }
    }
    
    // MARK: - 获取组件的所有选项
    func getComponentOptions(componentId: Int64) throws -> [ComponentOption] {
        return try safeExecute { db in
            var result: [ComponentOption] = []
            let query = Tables.componentOptions.filter(ComponentOptionColumns.componentId == componentId && ComponentOptionColumns.isActive == true).order(ComponentOptionColumns.sortOrder)
            for row in try db.prepare(query) {
                result.append(ComponentOption(
                    id: row[ComponentOptionColumns.id],
                    componentId: row[ComponentOptionColumns.componentId],
                    optionValue: row[ComponentOptionColumns.optionValue],
                    optionDisplay: row[ComponentOptionColumns.optionDisplay],
                    metadata: row[ComponentOptionColumns.metadata],
                    isActive: row[ComponentOptionColumns.isActive],
                    sortOrder: row[ComponentOptionColumns.sortOrder],
                    createdAt: row[ComponentOptionColumns.createdAt]
                ))
            }
            return result
        }
    }
    
    // MARK: - 获取所有组件选项（包括禁用的）
    func getAllComponentOptions(componentId: Int64) throws -> [ComponentOption] {
        return try safeExecute { db in
            var result: [ComponentOption] = []
            let query = Tables.componentOptions.filter(ComponentOptionColumns.componentId == componentId).order(ComponentOptionColumns.sortOrder)
            for row in try db.prepare(query) {
                result.append(ComponentOption(
                    id: row[ComponentOptionColumns.id],
                    componentId: row[ComponentOptionColumns.componentId],
                    optionValue: row[ComponentOptionColumns.optionValue],
                    optionDisplay: row[ComponentOptionColumns.optionDisplay],
                    metadata: row[ComponentOptionColumns.metadata],
                    isActive: row[ComponentOptionColumns.isActive],
                    sortOrder: row[ComponentOptionColumns.sortOrder],
                    createdAt: row[ComponentOptionColumns.createdAt]
                ))
            }
            return result
        }
    }
    
    // MARK: - 更新组件选项
    func updateComponentOption(id: Int64, option: ComponentOption) throws -> Bool {
        return try safeExecute { db in
            let opt = Tables.componentOptions.filter(ComponentOptionColumns.id == id)
            let update = opt.update(
                ComponentOptionColumns.optionValue <- option.optionValue,
                ComponentOptionColumns.optionDisplay <- option.optionDisplay,
                ComponentOptionColumns.metadata <- option.metadata,
                ComponentOptionColumns.isActive <- option.isActive,
                ComponentOptionColumns.sortOrder <- option.sortOrder
            )
            return try db.run(update) > 0
        }
    }
    
    // MARK: - 删除组件选项
    func deleteComponentOption(id: Int64) throws -> Bool {
        return try safeExecute { db in
            let option = Tables.componentOptions.filter(ComponentOptionColumns.id == id)
            return try db.run(option.delete()) > 0
        }
    }
    
    // MARK: - 禁用/启用组件选项
    func toggleComponentOption(id: Int64, isActive: Bool) throws -> Bool {
        return try safeExecute { db in
            let option = Tables.componentOptions.filter(ComponentOptionColumns.id == id)
            let update = option.update(ComponentOptionColumns.isActive <- isActive)
            return try db.run(update) > 0
        }
    }
    
    // MARK: - 获取组件的最大排序值
    func getMaxSortOrder(componentId: Int64) throws -> Int {
        return try safeExecute { db in
            let maxSort = try db.scalar(Tables.componentOptions.filter(ComponentOptionColumns.componentId == componentId).select(ComponentOptionColumns.sortOrder.max))
            return maxSort ?? 0
        }
    }
} 