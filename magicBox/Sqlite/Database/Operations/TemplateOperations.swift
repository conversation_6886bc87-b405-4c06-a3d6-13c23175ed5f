import SQLite
import Foundation

// MARK: - Template 相关操作
extension DatabaseManager {
    
    // MARK: - 创建模板
    func createTemplate(_ template: Template) throws -> Int64 {
        return try safeExecute { db in
            let insert = Tables.templates.insert(
                TemplateColumns.name <- template.name,
                TemplateColumns.description <- template.description,
                TemplateColumns.createdAt <- template.createdAt,
                TemplateColumns.updatedAt <- template.updatedAt
            )
            return try db.run(insert)
        }
    }
    
    // MARK: - 获取所有模板
    func getAllTemplates() throws -> [Template] {
        return try safeExecute { db in
            var result: [Template] = []
            for row in try db.prepare(Tables.templates.order(TemplateColumns.createdAt.desc)) {
                result.append(Template(
                    id: row[TemplateColumns.id],
                    name: row[TemplateColumns.name],
                    description: row[TemplateColumns.description],
                    createdAt: row[TemplateColumns.createdAt],
                    updatedAt: row[TemplateColumns.updatedAt]
                ))
            }
            return result
        }
    }
    
    // MARK: - 根据ID获取模板
    func getTemplate(id: Int64) throws -> Template? {
        return try safeExecute { db in
            let query = Tables.templates.filter(TemplateColumns.id == id)
            if let row = try db.pluck(query) {
                return Template(
                    id: row[TemplateColumns.id],
                    name: row[TemplateColumns.name],
                    description: row[TemplateColumns.description],
                    createdAt: row[TemplateColumns.createdAt],
                    updatedAt: row[TemplateColumns.updatedAt]
                )
            }
            return nil
        }
    }
    
    // MARK: - 更新模板
    func updateTemplate(id: Int64, name: String, description: String?) throws -> Bool {
        return try safeExecute { db in
            let template = Tables.templates.filter(TemplateColumns.id == id)
            let update = template.update(
                TemplateColumns.name <- name,
                TemplateColumns.description <- description,
                TemplateColumns.updatedAt <- Date()
            )
            return try db.run(update) > 0
        }
    }
    
    // MARK: - 删除模板
    func deleteTemplate(id: Int64) throws -> Bool {
        return try safeExecute { db in
            let template = Tables.templates.filter(TemplateColumns.id == id)
            return try db.run(template.delete()) > 0
        }
    }
    
    // MARK: - 检查模板名是否存在
    func templateNameExists(_ name: String, excludeId: Int64? = nil) throws -> Bool {
        return try safeExecute { db in
            var query = Tables.templates.filter(TemplateColumns.name == name)
            if let excludeId = excludeId {
                query = query.filter(TemplateColumns.id != excludeId)
            }
            return try db.scalar(query.count) > 0
        }
    }
    
    // MARK: - 获取模板使用统计
    func getTemplateUsageStats(templateId: Int64) throws -> [String: Any] {
        return try safeExecute { db in
            var stats: [String: Any] = [:]
            
            // 统计使用该模板的物品数量
            let itemCount = try db.scalar(Tables.items.filter(ItemColumns.templateId == templateId).count)
            stats["itemCount"] = itemCount
            
            // 统计组件数量
            let componentCount = try db.scalar(Tables.templateComponents.filter(TemplateComponentColumns.templateId == templateId).count)
            stats["componentCount"] = componentCount
            
            // 统计预设选项数量
            let optionCount = try db.scalar(
                Tables.componentOptions
                    .join(Tables.templateComponents, on: ComponentOptionColumns.componentId == TemplateComponentColumns.id)
                    .filter(TemplateComponentColumns.templateId == templateId)
                    .count
            )
            stats["optionCount"] = optionCount
            
            return stats
        }
    }
} 