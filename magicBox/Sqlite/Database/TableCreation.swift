import SQLite
import Foundation

// MARK: - 表创建逻辑
extension DatabaseManager {
    
    // MARK: - 创建 Templates 表
    func createTemplatesTable() throws {
        guard let db = db else { 
            throw DatabaseError.connectionFailed 
        }
        
        try db.run(Tables.templates.create(ifNotExists: true) { t in
            t.column(TemplateColumns.id, primaryKey: .autoincrement)
            t.column(TemplateColumns.name)
            t.column(TemplateColumns.description)
            t.column(TemplateColumns.createdAt, defaultValue: Date())
            t.column(TemplateColumns.updatedAt, defaultValue: Date())
        })
    }
    
    // MARK: - 创建 Template Components 表
    func createTemplateComponentsTable() throws {
        guard let db = db else { 
            throw DatabaseError.connectionFailed 
        }
        
        try db.run(Tables.templateComponents.create(ifNotExists: true) { t in
            t.column(TemplateComponentColumns.id, primaryKey: .autoincrement)
            t.column(TemplateComponentColumns.templateId)
            t.column(TemplateComponentColumns.componentName)
            t.column(TemplateComponentColumns.componentTitle)
            t.column(TemplateComponentColumns.componentType)
            t.column(TemplateComponentColumns.isRequired, defaultValue: false)
            t.column(TemplateComponentColumns.isMultiValue, defaultValue: false)
            t.column(TemplateComponentColumns.hasPredefinedOptions, defaultValue: false)
            t.column(TemplateComponentColumns.allowCustomValue, defaultValue: true)
            t.column(TemplateComponentColumns.sortOrder, defaultValue: 0)
            t.column(TemplateComponentColumns.defaultValue)
            t.foreignKey(TemplateComponentColumns.templateId, references: Tables.templates, TemplateColumns.id, delete: .cascade)
        })
    }
    
    // MARK: - 创建 Component Options 表
    func createComponentOptionsTable() throws {
        guard let db = db else { 
            throw DatabaseError.connectionFailed 
        }
        
        try db.run(Tables.componentOptions.create(ifNotExists: true) { t in
            t.column(ComponentOptionColumns.id, primaryKey: .autoincrement)
            t.column(ComponentOptionColumns.componentId)
            t.column(ComponentOptionColumns.optionValue)
            t.column(ComponentOptionColumns.optionDisplay)
            t.column(ComponentOptionColumns.metadata)
            t.column(ComponentOptionColumns.isActive, defaultValue: true)
            t.column(ComponentOptionColumns.sortOrder, defaultValue: 0)
            t.column(ComponentOptionColumns.createdAt, defaultValue: Date())
            t.foreignKey(ComponentOptionColumns.componentId, references: Tables.templateComponents, TemplateComponentColumns.id, delete: .cascade)
        })
    }
    
    // MARK: - 创建 Items 表
    func createItemsTable() throws {
        guard let db = db else { 
            throw DatabaseError.connectionFailed 
        }
        
        try db.run(Tables.items.create(ifNotExists: true) { t in
            t.column(ItemColumns.id, primaryKey: .autoincrement)
            t.column(ItemColumns.templateId)
            t.column(ItemColumns.createdAt, defaultValue: Date())
            t.column(ItemColumns.updatedAt, defaultValue: Date())
            t.foreignKey(ItemColumns.templateId, references: Tables.templates, TemplateColumns.id, delete: .cascade)
        })
    }
    
    // MARK: - 创建 Item Attribute Values 表
    func createItemAttributeValuesTable() throws {
        guard let db = db else { 
            throw DatabaseError.connectionFailed 
        }
        
        try db.run(Tables.itemAttributeValues.create(ifNotExists: true) { t in
            t.column(ItemAttributeValueColumns.id, primaryKey: .autoincrement)
            t.column(ItemAttributeValueColumns.itemId)
            t.column(ItemAttributeValueColumns.componentId)
            t.column(ItemAttributeValueColumns.value)
            t.column(ItemAttributeValueColumns.sortOrder, defaultValue: 0)
            t.foreignKey(ItemAttributeValueColumns.itemId, references: Tables.items, ItemColumns.id, delete: .cascade)
            t.foreignKey(ItemAttributeValueColumns.componentId, references: Tables.templateComponents, TemplateComponentColumns.id, delete: .cascade)
        })
    }
} 