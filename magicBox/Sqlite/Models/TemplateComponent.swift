import Foundation

// MARK: - 模板组件模型
/// 定义模板中的单个组件及其配置
struct TemplateComponent {
    /// 组件的唯一标识符
    let id: Int64?
    /// 所属模板的ID
    let templateId: Int64
    /// 组件名称（用于程序内部识别，应保持唯一）
    let componentName: String
    /// 组件标题（用于用户界面显示）
    let componentTitle: String
    /// 组件类型（如：string、number、date、color、boolean等）
    let componentType: String
    /// 是否为必填项
    let isRequired: Bool
    /// 是否支持多值（可以选择多个选项）
    let isMultiValue: Bool
    /// 是否有预定义选项
    let hasPredefinedOptions: Bool
    /// 是否允许自定义值（在有预定义选项时）
    let allowCustomValue: Bool
    /// 组件在模板中的排序顺序
    let sortOrder: Int
    /// 组件的默认值（可选）
    let defaultValue: String?
    
    /// 创建新组件的初始化方法
    /// - Parameters:
    ///   - templateId: 所属模板的ID
    ///   - componentName: 组件名称
    ///   - componentTitle: 组件标题
    ///   - componentType: 组件类型
    ///   - isRequired: 是否必填，默认为false
    ///   - isMultiValue: 是否支持多值，默认为false
    ///   - hasPredefinedOptions: 是否有预定义选项，默认为false
    ///   - allowCustomValue: 是否允许自定义值，默认为true
    ///   - sortOrder: 排序顺序，默认为0
    ///   - defaultValue: 默认值（可选）
    init(templateId: Int64, componentName: String, componentTitle: String, componentType: String, isRequired: Bool = false, isMultiValue: Bool = false, hasPredefinedOptions: Bool = false, allowCustomValue: Bool = true, sortOrder: Int = 0, defaultValue: String? = nil) {
        self.id = nil
        self.templateId = templateId
        self.componentName = componentName
        self.componentTitle = componentTitle
        self.componentType = componentType
        self.isRequired = isRequired
        self.isMultiValue = isMultiValue
        self.hasPredefinedOptions = hasPredefinedOptions
        self.allowCustomValue = allowCustomValue
        self.sortOrder = sortOrder
        self.defaultValue = defaultValue
    }
    
    /// 从数据库加载组件的初始化方法
    /// - Parameters:
    ///   - id: 组件的唯一标识符
    ///   - templateId: 所属模板的ID
    ///   - componentName: 组件名称
    ///   - componentTitle: 组件标题
    ///   - componentType: 组件类型
    ///   - isRequired: 是否必填
    ///   - isMultiValue: 是否支持多值
    ///   - hasPredefinedOptions: 是否有预定义选项
    ///   - allowCustomValue: 是否允许自定义值
    ///   - sortOrder: 排序顺序
    ///   - defaultValue: 默认值
    init(id: Int64, templateId: Int64, componentName: String, componentTitle: String, componentType: String, isRequired: Bool, isMultiValue: Bool, hasPredefinedOptions: Bool, allowCustomValue: Bool, sortOrder: Int, defaultValue: String?) {
        self.id = id
        self.templateId = templateId
        self.componentName = componentName
        self.componentTitle = componentTitle
        self.componentType = componentType
        self.isRequired = isRequired
        self.isMultiValue = isMultiValue
        self.hasPredefinedOptions = hasPredefinedOptions
        self.allowCustomValue = allowCustomValue
        self.sortOrder = sortOrder
        self.defaultValue = defaultValue
    }
}

// MARK: - 模板组件扩展
extension TemplateComponent {
    /// 获取显示用的组件标题
    var displayTitle: String {
        return componentTitle
    }
    
    /// 判断是否为颜色类型组件
    var isColorType: Bool {
        return componentType == "color"
    }
    
    /// 判断是否为字符串类型组件
    var isStringType: Bool {
        return componentType == "string"
    }
    
    /// 判断是否为日期类型组件
    var isDateType: Bool {
        return componentType == "date"
    }
    
    /// 判断是否为数字类型组件
    var isNumberType: Bool {
        return componentType == "number"
    }
    
    /// 判断是否为布尔类型组件
    var isBooleanType: Bool {
        return componentType == "boolean"
    }
    
    /// 判断是否为文本类型组件
    var isTextType: Bool {
        return componentType == "text"
    }
    
    /// 判断是否只能选择预定义选项（不允许自定义值）
    var requiresOptions: Bool {
        return hasPredefinedOptions && !allowCustomValue
    }
    
    /// 判断是否支持自定义输入
    var supportsCustomInput: Bool {
        return !hasPredefinedOptions || allowCustomValue
    }
} 