import Foundation

// MARK: - 物品属性值模型
/// 存储物品某个组件的具体属性值
struct ItemAttributeValue {
    /// 属性值的唯一标识符
    let id: Int64?
    /// 所属物品的ID
    let itemId: Int64
    /// 所属组件的ID
    let componentId: Int64
    /// 属性的具体值（以字符串形式存储）
    let value: String
    /// 属性值的排序顺序（用于多值属性的排序）
    let sortOrder: Int
    
    /// 创建新属性值的初始化方法
    /// - Parameters:
    ///   - itemId: 所属物品的ID
    ///   - componentId: 所属组件的ID
    ///   - value: 属性的具体值
    ///   - sortOrder: 排序顺序，默认为0
    init(itemId: Int64, componentId: Int64, value: String, sortOrder: Int = 0) {
        self.id = nil
        self.itemId = itemId
        self.componentId = componentId
        self.value = value
        self.sortOrder = sortOrder
    }
    
    /// 从数据库加载属性值的初始化方法
    /// - Parameters:
    ///   - id: 属性值的唯一标识符
    ///   - itemId: 所属物品的ID
    ///   - componentId: 所属组件的ID
    ///   - value: 属性的具体值
    ///   - sortOrder: 排序顺序
    init(id: Int64, itemId: Int64, componentId: Int64, value: String, sortOrder: Int) {
        self.id = id
        self.itemId = itemId
        self.componentId = componentId
        self.value = value
        self.sortOrder = sortOrder
    }
}

// MARK: - 物品属性值扩展
extension ItemAttributeValue {
    /// 获取显示用的值
    var displayValue: String {
        return value
    }
    
    /// 判断值是否为颜色代码格式
    var isColorValue: Bool {
        return value.hasPrefix("#") && value.count == 7
    }
    
    /// 获取颜色值（如果是颜色格式）
    var colorValue: String? {
        return isColorValue ? value : nil
    }
    
    /// 将字符串值转换为布尔值
    var boolValue: Bool {
        return value == "1" || value.lowercased() == "true"
    }
    
    /// 将字符串值转换为整数（如果可能）
    var intValue: Int? {
        return Int(value)
    }
    
    /// 将字符串值转换为浮点数（如果可能）
    var doubleValue: Double? {
        return Double(value)
    }
    
    /// 将字符串值转换为日期（如果符合yyyy-MM-dd格式）
    var dateValue: Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.date(from: value)
    }
} 