import Foundation

// MARK: - 组件选项模型
/// 用于存储模板组件的预定义选项
struct ComponentOption {
    /// 选项的唯一标识符
    let id: Int64?
    /// 所属组件的ID
    let componentId: Int64
    /// 选项的实际值（存储在数据库中的值）
    let optionValue: String
    /// 选项的显示名称（用户界面中显示的文本）
    let optionDisplay: String
    /// 选项的扩展属性（JSON格式，用于存储额外信息）
    let metadata: String?
    /// 选项是否激活状态
    let isActive: Bool
    /// 选项的排序顺序
    let sortOrder: Int
    /// 选项的创建时间
    let createdAt: Date
    
    /// 创建新选项的初始化方法
    /// - Parameters:
    ///   - componentId: 所属组件的ID
    ///   - optionValue: 选项的实际值
    ///   - optionDisplay: 选项的显示名称
    ///   - metadata: 选项的扩展属性（JSON格式，可选）
    ///   - isActive: 选项是否激活，默认为true
    ///   - sortOrder: 排序顺序，默认为0
    init(componentId: Int64, optionValue: String, optionDisplay: String, metadata: String? = nil, isActive: Bool = true, sortOrder: Int = 0) {
        self.id = nil
        self.componentId = componentId
        self.optionValue = optionValue
        self.optionDisplay = optionDisplay
        self.metadata = metadata
        self.isActive = isActive
        self.sortOrder = sortOrder
        self.createdAt = Date()
    }
    
    /// 从数据库加载选项的初始化方法
    /// - Parameters:
    ///   - id: 选项的唯一标识符
    ///   - componentId: 所属组件的ID
    ///   - optionValue: 选项的实际值
    ///   - optionDisplay: 选项的显示名称
    ///   - metadata: 选项的扩展属性（JSON格式）
    ///   - isActive: 选项是否激活
    ///   - sortOrder: 排序顺序
    ///   - createdAt: 创建时间
    init(id: Int64, componentId: Int64, optionValue: String, optionDisplay: String, metadata: String?, isActive: Bool, sortOrder: Int, createdAt: Date) {
        self.id = id
        self.componentId = componentId
        self.optionValue = optionValue
        self.optionDisplay = optionDisplay
        self.metadata = metadata
        self.isActive = isActive
        self.sortOrder = sortOrder
        self.createdAt = createdAt
    }
}

// MARK: - 组件选项扩展
extension ComponentOption {
    /// 获取显示文本
    var displayText: String {
        return optionDisplay
    }
} 
