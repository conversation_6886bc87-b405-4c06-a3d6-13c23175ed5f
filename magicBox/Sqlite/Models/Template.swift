import Foundation

// MARK: - 模板模型
/// 定义物品的结构和组件配置
struct Template {
    /// 模板的唯一标识符
    let id: Int64?
    /// 模板名称
    let name: String
    /// 模板描述（可选）
    let description: String?
    /// 模板创建时间
    let createdAt: Date
    /// 模板最后更新时间
    let updatedAt: Date
    
    /// 创建新模板的初始化方法
    /// - Parameters:
    ///   - name: 模板名称
    ///   - description: 模板描述（可选）
    init(name: String, description: String? = nil) {
        self.id = nil
        self.name = name
        self.description = description
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    /// 从数据库加载模板的初始化方法
    /// - Parameters:
    ///   - id: 模板的唯一标识符
    ///   - name: 模板名称
    ///   - description: 模板描述
    ///   - createdAt: 创建时间
    ///   - updatedAt: 最后更新时间
    init(id: Int64, name: String, description: String?, createdAt: Date, updatedAt: Date) {
        self.id = id
        self.name = name
        self.description = description
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - 模板扩展
extension Template {
    /// 获取显示用的模板名称
    var displayName: String {
        return name
    }
    
    /// 判断模板是否有描述信息
    var hasDescription: Bool {
        return description != nil && !description!.isEmpty
    }
} 