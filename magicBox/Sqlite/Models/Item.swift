import Foundation

// MARK: - 物品模型
/// 代表基于模板创建的物品实例
struct Item {
    /// 物品的唯一标识符
    let id: Int64?
    /// 物品所基于的模板ID
    let templateId: Int64
    /// 物品的创建时间
    let createdAt: Date
    /// 物品的最后更新时间
    let updatedAt: Date
    
    /// 创建新物品的初始化方法
    /// - Parameter templateId: 所基于的模板ID
    init(templateId: Int64) {
        self.id = nil
        self.templateId = templateId
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    /// 从数据库加载物品的初始化方法
    /// - Parameters:
    ///   - id: 物品的唯一标识符
    ///   - templateId: 所基于的模板ID
    ///   - createdAt: 创建时间
    ///   - updatedAt: 最后更新时间
    init(id: Int64, templateId: Int64, createdAt: Date, updatedAt: Date) {
        self.id = id
        self.templateId = templateId
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - 物品扩展
extension Item {
    /// 判断是否为新创建的物品（尚未保存到数据库）
    var isNew: Bool {
        return id == nil
    }
    
    /// 获取格式化的创建时间字符串
    var createdTimeString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    /// 获取格式化的更新时间字符串
    var updatedTimeString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: updatedAt)
    }
} 