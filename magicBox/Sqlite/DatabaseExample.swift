import Foundation

// MARK: - 数据库使用示例
class DatabaseExample {
    
    private let dbManager = DatabaseManager.shared
    
    // MARK: - 初始化示例数据
    func setupExampleData() {
        do {
            // 重置数据库（仅用于演示）
            // dbManager.resetDatabase()
            
            // 1. 创建服装模板
            let clothingTemplate = Template(name: "服装", description: "个人服装管理模板")
            let templateId = try dbManager.createTemplate(clothingTemplate)
            print("✅ 创建模板成功，ID: \(templateId)")
            
            // 2. 为模板添加组件
            let nameComponent = TemplateComponent(
                templateId: templateId,
                componentName: "name",
                componentTitle: "服装名称",
                componentType: "string",
                isRequired: true,
                isMultiValue: false,
                hasPredefinedOptions: false,
                allowCustomValue: true,
                sortOrder: 1
            )
            let nameComponentId = try dbManager.createTemplateComponent(nameComponent)
            
            let brandComponent = TemplateComponent(
                templateId: templateId,
                componentName: "brand",
                componentTitle: "品牌",
                componentType: "string",
                isRequired: true,
                isMultiValue: false,
                hasPredefinedOptions: true,
                allowCustomValue: true,
                sortOrder: 2
            )
            let brandComponentId = try dbManager.createTemplateComponent(brandComponent)
            
            let colorComponent = TemplateComponent(
                templateId: templateId,
                componentName: "colors",
                componentTitle: "颜色",
                componentType: "color",
                isRequired: false,
                isMultiValue: true,
                hasPredefinedOptions: true,
                allowCustomValue: true,
                sortOrder: 3
            )
            let colorComponentId = try dbManager.createTemplateComponent(colorComponent)
            
            print("✅ 创建组件成功")
            
            // 3. 为品牌组件添加预设选项
            let brandOptions = [
                ComponentOption(componentId: brandComponentId, optionValue: "nike", optionDisplay: "Nike", sortOrder: 1),
                ComponentOption(componentId: brandComponentId, optionValue: "adidas", optionDisplay: "Adidas", sortOrder: 2),
                ComponentOption(componentId: brandComponentId, optionValue: "zara", optionDisplay: "Zara", sortOrder: 3),
                ComponentOption(componentId: brandComponentId, optionValue: "uniqlo", optionDisplay: "Uniqlo", sortOrder: 4)
            ]
            
            for option in brandOptions {
                try dbManager.createComponentOption(option)
            }
            
            // 4. 为颜色组件添加预设选项
            let colorOptions = [
                ComponentOption(componentId: colorComponentId, optionValue: "#FF0000", optionDisplay: "红色", sortOrder: 1),
                ComponentOption(componentId: colorComponentId, optionValue: "#00FF00", optionDisplay: "绿色", sortOrder: 2),
                ComponentOption(componentId: colorComponentId, optionValue: "#0000FF", optionDisplay: "蓝色", sortOrder: 3),
                ComponentOption(componentId: colorComponentId, optionValue: "#FFFF00", optionDisplay: "黄色", sortOrder: 4),
                ComponentOption(componentId: colorComponentId, optionValue: "#000000", optionDisplay: "黑色", sortOrder: 5)
            ]
            
            for option in colorOptions {
                try dbManager.createComponentOption(option)
            }
            
            print("✅ 创建预设选项成功")
            
            // 5. 创建物品并填写属性
            let item = Item(templateId: templateId)
            let itemId = try dbManager.createItem(item)
            
            // 填写属性值
            try dbManager.saveItemAttributeValues(itemId: itemId, componentId: nameComponentId, values: ["Nike运动T恤"])
            try dbManager.saveItemAttributeValues(itemId: itemId, componentId: brandComponentId, values: ["nike"])
            try dbManager.saveItemAttributeValues(itemId: itemId, componentId: colorComponentId, values: ["#FF0000", "#0000FF", "#FFFF00"])
            
            print("✅ 创建物品成功，ID: \(itemId)")
            
            // 6. 查询数据验证
            let itemDetails = try dbManager.getItemDetails(itemId: itemId)
            print("\n📋 物品详情：")
            for detail in itemDetails {
                print("  \(detail.component.componentTitle):")
                for value in detail.values {
                    let displayText = value.display ?? value.value
                    print("    - \(displayText) (\(value.value))")
                }
            }
            
        } catch {
            print("❌ 示例数据创建失败: \(error)")
        }
    }
    
    // MARK: - 演示设置页面逻辑
    func demonstrateSettingsPage() {
        do {
            print("\n🔧 设置页面演示：")
            
            // 获取所有模板
            let templates = try dbManager.getAllTemplates()
            print("所有模板：")
            for template in templates {
                print("  - \(template.name): \(template.description ?? "无描述")")
            }
            
            // 获取第一个模板的组件
            if let firstTemplate = templates.first {
                let components = try dbManager.getTemplateComponents(templateId: firstTemplate.id!)
                print("\n\(firstTemplate.name) 模板的组件：")
                for component in components {
                    print("  - \(component.componentTitle) (\(component.componentType))")
                    if component.hasPredefinedOptions {
                        let options = try dbManager.getComponentOptions(componentId: component.id!)
                        print("    预设选项：")
                        for option in options {
                            print("      * \(option.optionDisplay)")
                        }
                    }
                }
            }
            
        } catch {
            print("❌ 设置页面演示失败: \(error)")
        }
    }
    
    // MARK: - 演示物品页面逻辑
    func demonstrateItemPage() {
        do {
            print("\n📱 物品页面演示：")
            
            // 获取所有物品
            let items = try dbManager.getAllItems()
            print("所有物品数量: \(items.count)")
            
            for item in items {
                print("\n物品 #\(item.id!):")
                let details = try dbManager.getItemDetails(itemId: item.id!)
                for detail in details {
                    let values = detail.values.map { $0.display ?? $0.value }.joined(separator: ", ")
                    print("  \(detail.component.componentTitle): \(values)")
                }
            }
            
        } catch {
            print("❌ 物品页面演示失败: \(error)")
        }
    }
    
    // MARK: - 添加新的品牌选项（模拟设置页面操作）
    func addNewBrandOption(brandValue: String, brandDisplay: String) {
        do {
            // 找到品牌组件
            let templates = try dbManager.getAllTemplates()
            if let template = templates.first {
                let components = try dbManager.getTemplateComponents(templateId: template.id!)
                if let brandComponent = components.first(where: { $0.componentName == "brand" }) {
                    let maxSortOrder = try dbManager.getMaxSortOrder(componentId: brandComponent.id!)
                    let newOption = ComponentOption(
                        componentId: brandComponent.id!,
                        optionValue: brandValue,
                        optionDisplay: brandDisplay,
                        sortOrder: maxSortOrder + 1
                    )
                    try dbManager.createComponentOption(newOption)
                    print("✅ 添加品牌选项成功: \(brandDisplay)")
                }
            }
        } catch {
            print("❌ 添加品牌选项失败: \(error)")
        }
    }
    
    // MARK: - 添加新的颜色选项（模拟设置页面操作）
    func addNewColorOption(colorValue: String, colorDisplay: String) {
        do {
            // 找到颜色组件
            let templates = try dbManager.getAllTemplates()
            if let template = templates.first {
                let components = try dbManager.getTemplateComponents(templateId: template.id!)
                if let colorComponent = components.first(where: { $0.componentName == "colors" }) {
                    let maxSortOrder = try dbManager.getMaxSortOrder(componentId: colorComponent.id!)
                    let newOption = ComponentOption(
                        componentId: colorComponent.id!,
                        optionValue: colorValue,
                        optionDisplay: colorDisplay,
                        sortOrder: maxSortOrder + 1
                    )
                    try dbManager.createComponentOption(newOption)
                    print("✅ 添加颜色选项成功: \(colorDisplay) (\(colorValue))")
                }
            }
        } catch {
            print("❌ 添加颜色选项失败: \(error)")
        }
    }
    
    // MARK: - 获取数据库统计信息
    func showDatabaseStats() {
        let info = dbManager.getDatabaseInfo()
        print("\n📊 数据库统计信息：")
        for (key, value) in info {
            print("  \(key): \(value)")
        }
    }
    
    // MARK: - 运行完整演示
    func runFullDemo() {
        print("🚀 开始数据库演示...")
        
        setupExampleData()
        demonstrateSettingsPage()
        demonstrateItemPage()
        
        print("\n➕ 添加新选项演示：")
        addNewBrandOption(brandValue: "hm", brandDisplay: "H&M")
        addNewColorOption(colorValue: "#800080", colorDisplay: "紫色")
        
        showDatabaseStats()
        
        print("\n✨ 演示完成！")
    }
} 