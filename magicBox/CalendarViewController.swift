//
//  CalendarViewController.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON>an on 2025/5/18.
//

import UIKit
import EventKit

class CalendarViewController: BaseViewController {
    
    private var calendarView: UICalendarView!
    private var tableView: UITableView!
    private var selectedDate = Date()
    private var events: [CalendarEvent] = []
    private let eventStore = EKEventStore()
    
    // 自定义事件模型
    struct CalendarEvent {
        let id: String
        let title: String
        let date: Date
        let time: String
        let description: String
        let color: UIColor
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        loadSampleEvents()
        requestCalendarAccess()
    }
    
    private func setupUI() {
        title = NSLocalizedString("calendar.title", comment: "Calendar")
        
        setupCalendarView()
        setupTableView()
        setupConstraints()
    }
    
    private func setupNavigationBar() {
        // 添加导航栏按钮
        let addButton = UIBarButtonItem(
            barButtonSystemItem: .add,
            target: self,
            action: #selector(addEventTapped)
        )
        
        let todayButton = UIBarButtonItem(
            title: NSLocalizedString("calendar.today", comment: "Today"),
            style: .plain,
            target: self,
            action: #selector(todayTapped)
        )
        
        navigationItem.rightBarButtonItems = [addButton]
        navigationItem.leftBarButtonItem = todayButton
    }
    
    private func setupCalendarView() {
        calendarView = UICalendarView()
        calendarView.delegate = self
        calendarView.calendar = Calendar(identifier: .gregorian)
        calendarView.availableDateRange = DateInterval(start: .distantPast, end: .distantFuture)
        calendarView.fontDesign = .rounded
        calendarView.translatesAutoresizingMaskIntoConstraints = false
        
        // 设置日历选择
        let selection = UICalendarSelectionSingleDate(delegate: self)
        selection.selectedDate = Calendar.current.dateComponents([.year, .month, .day], from: selectedDate)
        calendarView.selectionBehavior = selection
        
        // 确保日历能够完整显示
        calendarView.setContentCompressionResistancePriority(.required, for: .vertical)
        calendarView.setContentHuggingPriority(.required, for: .vertical)
        
        view.addSubview(calendarView)
    }
    
    private func setupTableView() {
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.translatesAutoresizingMaskIntoConstraints = false
        tableView.backgroundColor = AppTheme.Colors.groupedBackground
        
        // 注册cell
        tableView.register(EventTableViewCell.self, forCellReuseIdentifier: "EventCell")
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "EmptyCell")
        
        view.addSubview(tableView)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 日历视图约束 - 使用更大的固定高度确保完整显示
            calendarView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16),
            calendarView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            calendarView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            calendarView.heightAnchor.constraint(equalToConstant: 450),
            
            // 表格视图约束
            tableView.topAnchor.constraint(equalTo: calendarView.bottomAnchor, constant: 16),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func loadSampleEvents() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        
        events = [
            CalendarEvent(
                id: "1",
                title: NSLocalizedString("calendar.sample.team_meeting", comment: "Team Meeting"),
                date: Date(),
                time: "09:00",
                description: NSLocalizedString("calendar.sample.team_meeting_desc", comment: "Discuss project progress and next steps"),
                color: .systemBlue
            ),
            CalendarEvent(
                id: "2",
                title: NSLocalizedString("calendar.sample.fitness_training", comment: "Fitness Training"),
                date: Date(),
                time: "18:00",
                description: NSLocalizedString("calendar.sample.fitness_training_desc", comment: "Three times a week fitness plan"),
                color: .systemGreen
            ),
            CalendarEvent(
                id: "3",
                title: NSLocalizedString("calendar.sample.doctor_appointment", comment: "Doctor Appointment"),
                date: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date(),
                time: "14:30",
                description: NSLocalizedString("calendar.sample.doctor_appointment_desc", comment: "Annual physical examination"),
                color: .systemRed
            ),
            CalendarEvent(
                id: "4",
                title: NSLocalizedString("calendar.sample.friends_dinner", comment: "Friends Dinner"),
                date: Calendar.current.date(byAdding: .day, value: 2, to: Date()) ?? Date(),
                time: "19:00",
                description: NSLocalizedString("calendar.sample.friends_dinner_desc", comment: "Old classmates gathering"),
                color: .systemOrange
            )
        ]
    }
    
    private func requestCalendarAccess() {
        eventStore.requestAccess(to: .event) { granted, error in
            if granted {
                print("日历访问权限已获得")
            } else {
                print("日历访问权限被拒绝")
            }
        }
    }
    
    private func eventsForSelectedDate() -> [CalendarEvent] {
        let calendar = Calendar.current
        return events.filter { event in
            calendar.isDate(event.date, inSameDayAs: selectedDate)
        }.sorted { $0.time < $1.time }
    }
    
    @objc private func addEventTapped() {
        showAddEventAlert()
    }
    
    @objc private func todayTapped() {
        selectedDate = Date()
        let today = Calendar.current.dateComponents([.year, .month, .day], from: selectedDate)
        if let selection = calendarView.selectionBehavior as? UICalendarSelectionSingleDate {
            selection.selectedDate = today
        }
        tableView.reloadData()
    }
    
    private func showAddEventAlert() {
        let alert = UIAlertController(title: NSLocalizedString("calendar.add_event", comment: "Add Event"), message: String(format: NSLocalizedString("calendar.add_event_message", comment: "Add new event for %@"), formatDate(selectedDate)), preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = NSLocalizedString("calendar.event_title_placeholder", comment: "Event Title")
        }
        
        alert.addTextField { textField in
            textField.placeholder = NSLocalizedString("calendar.event_time_placeholder", comment: "Time (e.g. 09:00)")
        }
        
        alert.addTextField { textField in
            textField.placeholder = NSLocalizedString("calendar.event_description_placeholder", comment: "Description (Optional)")
        }
        
        let addAction = UIAlertAction(title: NSLocalizedString("calendar.add", comment: "Add"), style: .default) { _ in
            guard let titleField = alert.textFields?[0],
                  let timeField = alert.textFields?[1],
                  let descriptionField = alert.textFields?[2],
                  let title = titleField.text, !title.isEmpty,
                  let time = timeField.text, !time.isEmpty else {
                return
            }
            
            let newEvent = CalendarEvent(
                id: UUID().uuidString,
                title: title,
                date: self.selectedDate,
                time: time,
                description: descriptionField.text ?? "",
                color: AppTheme.Colors.accent
            )
            
            self.events.append(newEvent)
            self.tableView.reloadData()
            self.calendarView.reloadDecorations(forDateComponents: [Calendar.current.dateComponents([.year, .month, .day], from: self.selectedDate)], animated: true)
        }
        
        alert.addAction(addAction)
        alert.addAction(UIAlertAction(title: NSLocalizedString("calendar.cancel", comment: "Cancel"), style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - UICalendarViewDelegate
extension CalendarViewController: UICalendarViewDelegate {
    func calendarView(_ calendarView: UICalendarView, decorationFor dateComponents: DateComponents) -> UICalendarView.Decoration? {
        guard let date = Calendar.current.date(from: dateComponents) else { return nil }
        
        let dayEvents = events.filter { event in
            Calendar.current.isDate(event.date, inSameDayAs: date)
        }
        
        if !dayEvents.isEmpty {
            return .default(color: AppTheme.Colors.primary, size: .small)
        }
        
        return nil
    }
}

// MARK: - UICalendarSelectionSingleDateDelegate
extension CalendarViewController: UICalendarSelectionSingleDateDelegate {
    func dateSelection(_ selection: UICalendarSelectionSingleDate, didSelectDate dateComponents: DateComponents?) {
        guard let dateComponents = dateComponents,
              let date = Calendar.current.date(from: dateComponents) else { return }
        
        selectedDate = date
        tableView.reloadData()
    }
}

// MARK: - UITableView DataSource & Delegate
extension CalendarViewController: UITableViewDataSource, UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let dayEvents = eventsForSelectedDate()
        return dayEvents.isEmpty ? 1 : dayEvents.count
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return String(format: NSLocalizedString("calendar.schedule_for_date", comment: "%@'s Schedule"), formatDate(selectedDate))
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let dayEvents = eventsForSelectedDate()
        
        if dayEvents.isEmpty {
            let cell = tableView.dequeueReusableCell(withIdentifier: "EmptyCell", for: indexPath)
            cell.textLabel?.text = NSLocalizedString("calendar.no_events", comment: "No events")
            cell.textLabel?.textColor = .systemGray
            cell.textLabel?.textAlignment = .center
            cell.selectionStyle = .none
            return cell
        }
        
        let cell = tableView.dequeueReusableCell(withIdentifier: "EventCell", for: indexPath) as! EventTableViewCell
        let event = dayEvents[indexPath.row]
        cell.configure(with: event)
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let dayEvents = eventsForSelectedDate()
        guard !dayEvents.isEmpty else { return }
        
        let event = dayEvents[indexPath.row]
        showEventDetail(event)
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            let dayEvents = eventsForSelectedDate()
            guard !dayEvents.isEmpty else { return }
            
            let eventToDelete = dayEvents[indexPath.row]
            events.removeAll { $0.id == eventToDelete.id }
            
            tableView.reloadData()
            calendarView.reloadDecorations(forDateComponents: [Calendar.current.dateComponents([.year, .month, .day], from: selectedDate)], animated: true)
        }
    }
    
    private func showEventDetail(_ event: CalendarEvent) {
        let alert = UIAlertController(title: event.title, message: "\(event.time)\n\n\(event.description)", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("calendar.ok", comment: "OK"), style: .default))
        present(alert, animated: true)
    }
}

// MARK: - Custom Event Cell
class EventTableViewCell: UITableViewCell {
    
    private let colorIndicator = UIView()
    private let timeLabel = UILabel()
    private let titleLabel = UILabel()
    private let descriptionLabel = UILabel()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCell() {
        // 颜色指示器
        colorIndicator.layer.cornerRadius = 3
        colorIndicator.translatesAutoresizingMaskIntoConstraints = false
        
        // 时间标签
        timeLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        timeLabel.textColor = AppTheme.Colors.secondaryLabel
        timeLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 标题标签
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = AppTheme.Colors.label
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 描述标签
        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.textColor = AppTheme.Colors.tertiaryLabel
        descriptionLabel.numberOfLines = 2
        descriptionLabel.translatesAutoresizingMaskIntoConstraints = false
        
        contentView.addSubview(colorIndicator)
        contentView.addSubview(timeLabel)
        contentView.addSubview(titleLabel)
        contentView.addSubview(descriptionLabel)
        
        NSLayoutConstraint.activate([
            colorIndicator.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            colorIndicator.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            colorIndicator.widthAnchor.constraint(equalToConstant: 6),
            colorIndicator.heightAnchor.constraint(equalToConstant: 32),
            
            timeLabel.leadingAnchor.constraint(equalTo: colorIndicator.trailingAnchor, constant: 12),
            timeLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            timeLabel.widthAnchor.constraint(equalToConstant: 60),
            
            titleLabel.leadingAnchor.constraint(equalTo: timeLabel.trailingAnchor, constant: 12),
            titleLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            titleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            
            descriptionLabel.leadingAnchor.constraint(equalTo: titleLabel.leadingAnchor),
            descriptionLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            descriptionLabel.trailingAnchor.constraint(equalTo: titleLabel.trailingAnchor),
            descriptionLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -12)
        ])
    }
    
    func configure(with event: CalendarViewController.CalendarEvent) {
        colorIndicator.backgroundColor = event.color
        timeLabel.text = event.time
        titleLabel.text = event.title
        descriptionLabel.text = event.description.isEmpty ? NSLocalizedString("calendar.no_description", comment: "No description") : event.description
    }
} 
