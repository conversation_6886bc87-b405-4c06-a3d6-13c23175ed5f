/* 
   Localizable.strings
   magicBox

   Created by AI on 2025/1/19.
   
*/

// MARK: - Tab Bar Items
"tab.home" = "Home";
"tab.calendar" = "Calendar";
"tab.statistics" = "Statistics";
"tab.settings" = "Settings";

// MARK: - Home Screen
"home.title" = "Home";
"home.menu.title" = "Feature Menu";
"home.menu.database_example" = "Database Example";
"home.add.photo" = "Add from Photos";

// MARK: - Calendar Screen
"calendar.title" = "Calendar";
"calendar.today" = "Today";
"calendar.add_event" = "Add Event";
"calendar.add_event_message" = "Add new event for %@";
"calendar.event_title_placeholder" = "Event Title";
"calendar.event_time_placeholder" = "Time (e.g. 09:00)";
"calendar.event_description_placeholder" = "Description (Optional)";
"calendar.add" = "Add";
"calendar.cancel" = "Cancel";
"calendar.ok" = "OK";
"calendar.schedule_for_date" = "%@'s Schedule";
"calendar.no_events" = "No events";
"calendar.no_description" = "No description";

// MARK: - Calendar Sample Events
"calendar.sample.team_meeting" = "Team Meeting";
"calendar.sample.team_meeting_desc" = "Discuss project progress and next steps";
"calendar.sample.fitness_training" = "Fitness Training";
"calendar.sample.fitness_training_desc" = "Three times a week fitness plan";
"calendar.sample.doctor_appointment" = "Doctor Appointment";
"calendar.sample.doctor_appointment_desc" = "Annual physical examination";
"calendar.sample.friends_dinner" = "Friends Dinner";
"calendar.sample.friends_dinner_desc" = "Old classmates gathering";

// MARK: - Statistics Screen
"statistics.title" = "Statistics";
"statistics.monthly_trend" = "Monthly Trend";
"statistics.category_distribution" = "Category Distribution";
"statistics.data_statistics" = "Data Statistics";
"statistics.description" = "View your usage data and analysis reports";

// MARK: - Settings Screen
"settings.title" = "Settings";

// MARK: - Notification Settings
"settings.notification.title" = "Notification Settings";
"settings.notification.push_notification" = "Push Notifications";
"settings.notification.push_notification_subtitle" = "Receive important reminders";
"settings.notification.calendar_reminder" = "Calendar Reminders";
"settings.notification.calendar_reminder_subtitle" = "Schedule event reminders";

// MARK: - Appearance Settings
"settings.appearance.title" = "Appearance Settings";
"settings.appearance.dark_mode" = "Dark Mode";
"settings.appearance.dark_mode_subtitle" = "Follow system or manual setting";
"settings.appearance.font_size" = "Font Size";
"settings.appearance.font_size_subtitle" = "Adjust app font size";
"settings.appearance.color_management" = "Color Management";
"settings.appearance.color_management_subtitle" = "Manage color options in the app";

// MARK: - Data Management
"settings.data.title" = "Data Management";
"settings.data.sync" = "Data Sync";
"settings.data.sync_subtitle" = "iCloud sync";
"settings.data.clear_cache" = "Clear Cache";
"settings.data.clear_cache_subtitle" = "Free up storage space";
"settings.data.export_data" = "Export Data";
"settings.data.export_data_subtitle" = "Backup your data";

// MARK: - Security Settings
"settings.security.title" = "Security Settings";
"settings.security.biometric_auth" = "Biometric Authentication";
"settings.security.biometric_auth_subtitle" = "Use Face ID or Touch ID";

// MARK: - About
"settings.about.title" = "About";
"settings.about.version" = "Version";
"settings.about.privacy_policy" = "Privacy Policy";
"settings.about.privacy_policy_subtitle" = "View our privacy policy";
"settings.about.feedback" = "Feedback";
"settings.about.feedback_subtitle" = "Send suggestions and feedback";
"settings.about.rate_app" = "Rate App";
"settings.about.rate_app_subtitle" = "Rate us on the App Store";

// MARK: - Settings Alerts
"settings.notification.enable_title" = "Notification Settings";
"settings.notification.enable_message" = "Enable push notifications?";
"settings.theme.title" = "Appearance Settings";
"settings.theme.message" = "Select app theme";
"settings.theme.follow_system" = "Follow System";
"settings.theme.light_mode" = "Light Mode";
"settings.theme.dark_mode" = "Dark Mode";
"settings.font.title" = "Font Settings";
"settings.font.message" = "Adjust font size";
"settings.cache.clear_title" = "Clear Cache";
"settings.cache.clear_message" = "Are you sure you want to clear all cache data?";
"settings.cache.clear_action" = "Clear";
"settings.cache.cleared_message" = "Cache has been cleared";
"settings.export.title" = "Export Data";
"settings.export.message" = "Export data as JSON format";
"settings.export.action" = "Export";
"settings.privacy.title" = "Privacy Policy";
"settings.privacy.message" = "View our privacy policy";
"settings.feedback.title" = "Feedback";
"settings.feedback.message" = "Thank you for your feedback!";
"settings.rate.title" = "Rate App";
"settings.rate.message" = "If you like this app, please rate us on the App Store!";
"settings.rate.later" = "Remind Later";
"settings.rate.rate_now" = "Rate Now";

// MARK: - Color Settings
"colors.title" = "Color Management";
"colors.predefined_colors" = "Predefined Colors";
"colors.custom_colors" = "Custom Colors";
"colors.add_color" = "Add Color";
"colors.add_color_title" = "Add New Color";
"colors.add_color_message" = "Please enter color information";
"colors.color_name_placeholder" = "Color Name";
"colors.color_value_placeholder" = "Color Value (#RRGGBB)";
"colors.save" = "Save";
"colors.edit" = "Edit";
"colors.delete" = "Delete";
"colors.edit_color_title" = "Edit Color";
"colors.edit_color_message" = "Modify color information";
"colors.delete_color_title" = "Delete Color";
"colors.delete_color_message" = "Are you sure you want to delete this color?";
"colors.invalid_color_format" = "Invalid color format";
"colors.color_name_required" = "Color name is required";
"colors.color_saved" = "Color saved successfully";
"colors.color_deleted" = "Color deleted successfully";
"colors.color_exists" = "Color name already exists";
"colors.load_error" = "Failed to load color data";
"colors.add_error" = "Failed to add color";
"colors.delete_error" = "Failed to delete color";
"colors.update_error" = "Failed to update color";

// MARK: - Common
"common.cancel" = "Cancel";
"common.ok" = "OK";
"common.save" = "Save";
"common.edit" = "Edit";
"common.delete" = "Delete";
"common.add" = "Add";
"common.done" = "Done";
"common.close" = "Close";
"common.confirm" = "Confirm";
"common.error" = "Error";
"common.success" = "Success";
"common.warning" = "Warning";
"common.info" = "Info"; 

// Options Management
"options.title" = "Options Management";
"options.appearance.title" = "Appearance";
"options.colors.title" = "Colors";
"options.colors.subtitle" = "Manage color options";

// Settings
"settings.appearance.options" = "Options Management";
"settings.appearance.options_subtitle" = "Manage app options like colors, sizes, etc."; 

"permission.denied.title" = "Access Denied";
"permission.photos.denied.message" = "Please enable photos access in Settings to select photos from your library"; 

// 文字选项
"text_options.title" = "Text Management";
"text_options.section.title" = "Text Options";
"text_options.add_text" = "Add Text";
"text_options.add_text_title" = "Add New Text";
"text_options.add_text_message" = "Please enter text information";
"text_options.edit_text_title" = "Edit Text";
"text_options.edit_text_message" = "Modify text information";
"text_options.text_display_placeholder" = "Display Text";
"text_options.text_value_placeholder" = "Value";
"text_options.load_error" = "Failed to load text data";
"text_options.add_error" = "Failed to add text";
"text_options.update_error" = "Failed to update text";
"text_options.delete_error" = "Failed to delete text";

// 选项页面
"options.texts.title" = "Texts";
"options.texts.subtitle" = "Manage text options"; 