/* 
   Localizable.strings
   magicBox

   Created by AI on 2025/1/19.
   
*/

// MARK: - Tab Bar Items
"tab.home" = "首页";
"tab.calendar" = "日历";
"tab.statistics" = "统计";
"tab.settings" = "设置";

// MARK: - Home Screen
"home.title" = "首页";
"home.menu.title" = "功能菜单";
"home.menu.database_example" = "数据库示例";
"home.add.photo" = "从相册添加";

// MARK: - Calendar Screen
"calendar.title" = "日历";
"calendar.today" = "今天";
"calendar.add_event" = "添加事件";
"calendar.add_event_message" = "为 %@ 添加新事件";
"calendar.event_title_placeholder" = "事件标题";
"calendar.event_time_placeholder" = "时间 (如: 09:00)";
"calendar.event_description_placeholder" = "描述（可选）";
"calendar.add" = "添加";
"calendar.cancel" = "取消";
"calendar.ok" = "确定";
"calendar.schedule_for_date" = "%@ 的日程";
"calendar.no_events" = "没有安排";
"calendar.no_description" = "无描述";

// MARK: - Calendar Sample Events
"calendar.sample.team_meeting" = "团队会议";
"calendar.sample.team_meeting_desc" = "讨论项目进展和下一步计划";
"calendar.sample.fitness_training" = "健身训练";
"calendar.sample.fitness_training_desc" = "每周三次的健身计划";
"calendar.sample.doctor_appointment" = "医生预约";
"calendar.sample.doctor_appointment_desc" = "年度体检";
"calendar.sample.friends_dinner" = "朋友聚餐";
"calendar.sample.friends_dinner_desc" = "老同学聚会";

// MARK: - Statistics
"statistics.title" = "统计";
"statistics.monthly_trend" = "月度趋势";
"statistics.category_distribution" = "分类统计";
"statistics.data_statistics" = "数据统计";
"statistics.description" = "查看您的使用数据和分析报告";

// MARK: - Settings Screen
"settings.title" = "设置";

// MARK: - Notification Settings
"settings.notification.title" = "通知设置";
"settings.notification.push_notification" = "推送通知";
"settings.notification.push_notification_subtitle" = "接收重要提醒";
"settings.notification.calendar_reminder" = "日历提醒";
"settings.notification.calendar_reminder_subtitle" = "日程事件提醒";

// MARK: - Appearance Settings
"settings.appearance.title" = "外观设置";
"settings.appearance.dark_mode" = "深色模式";
"settings.appearance.dark_mode_subtitle" = "跟随系统或手动设置";
"settings.appearance.font_size" = "字体大小";
"settings.appearance.font_size_subtitle" = "调整应用字体大小";
"settings.appearance.color_management" = "颜色管理";
"settings.appearance.color_management_subtitle" = "管理应用中的颜色选项";

// MARK: - Data Management
"settings.data.title" = "数据管理";
"settings.data.sync" = "数据同步";
"settings.data.sync_subtitle" = "iCloud同步";
"settings.data.clear_cache" = "清除缓存";
"settings.data.clear_cache_subtitle" = "释放存储空间";
"settings.data.export_data" = "导出数据";
"settings.data.export_data_subtitle" = "备份您的数据";

// MARK: - Security Settings
"settings.security.title" = "安全设置";
"settings.security.biometric_auth" = "生物识别认证";
"settings.security.biometric_auth_subtitle" = "使用面容ID或触控ID";

// MARK: - About
"settings.about.title" = "关于";
"settings.about.version" = "版本";
"settings.about.privacy_policy" = "隐私政策";
"settings.about.privacy_policy_subtitle" = "查看我们的隐私政策";
"settings.about.feedback" = "反馈建议";
"settings.about.feedback_subtitle" = "发送建议和反馈";
"settings.about.rate_app" = "评价应用";
"settings.about.rate_app_subtitle" = "在App Store给我们评价";

// MARK: - Settings Alerts
"settings.notification.enable_title" = "通知设置";
"settings.notification.enable_message" = "是否开启推送通知？";
"settings.theme.title" = "外观设置";
"settings.theme.message" = "选择应用主题";
"settings.theme.follow_system" = "跟随系统";
"settings.theme.light_mode" = "浅色模式";
"settings.theme.dark_mode" = "深色模式";
"settings.font.title" = "字体设置";
"settings.font.message" = "调整字体大小";
"settings.cache.clear_title" = "清除缓存";
"settings.cache.clear_message" = "确定要清除所有缓存数据吗？";
"settings.cache.clear_action" = "清除";
"settings.cache.cleared_message" = "缓存已清除";
"settings.export.title" = "导出数据";
"settings.export.message" = "将数据导出为JSON格式";
"settings.export.action" = "导出";
"settings.privacy.title" = "隐私政策";
"settings.privacy.message" = "查看我们的隐私政策";
"settings.feedback.title" = "反馈建议";
"settings.feedback.message" = "感谢您的反馈！";
"settings.rate.title" = "评价应用";
"settings.rate.message" = "如果您喜欢这个应用，请在App Store给我们评价！";
"settings.rate.later" = "稍后提醒";
"settings.rate.rate_now" = "去评价";

// MARK: - Color Settings
"colors.title" = "颜色管理";
"colors.predefined_colors" = "预定义颜色";
"colors.custom_colors" = "自定义颜色";
"colors.add_color" = "添加颜色";
"colors.add_color_title" = "添加新颜色";
"colors.add_color_message" = "请输入颜色信息";
"colors.color_name_placeholder" = "颜色名称";
"colors.color_value_placeholder" = "颜色值 (#RRGGBB)";
"colors.save" = "保存";
"colors.edit" = "编辑";
"colors.delete" = "删除";
"colors.edit_color_title" = "编辑颜色";
"colors.edit_color_message" = "修改颜色信息";
"colors.delete_color_title" = "删除颜色";
"colors.delete_color_message" = "确定要删除这个颜色吗？";
"colors.invalid_color_format" = "无效的颜色格式";
"colors.color_name_required" = "颜色名称不能为空";
"colors.color_saved" = "颜色保存成功";
"colors.color_deleted" = "颜色删除成功";
"colors.color_exists" = "颜色名称已存在";
"colors.load_error" = "无法加载颜色数据";
"colors.add_error" = "无法添加颜色";
"colors.delete_error" = "无法删除颜色";
"colors.update_error" = "无法更新颜色";

// MARK: - Common
"common.cancel" = "取消";
"common.ok" = "确定";
"common.save" = "保存";
"common.edit" = "编辑";
"common.delete" = "删除";
"common.add" = "添加";
"common.done" = "完成";
"common.close" = "关闭";
"common.confirm" = "确认";
"common.error" = "错误";
"common.success" = "成功";
"common.warning" = "警告";
"common.info" = "信息"; 

// 选项管理
"options.title" = "选项管理";
"options.appearance.title" = "外观";
"options.colors.title" = "颜色";
"options.colors.subtitle" = "管理颜色选项";

// 文字选项
"text_options.title" = "文字管理";
"text_options.section.title" = "文字选项";
"text_options.add_text" = "添加文字";
"text_options.add_text_title" = "添加新文字";
"text_options.add_text_message" = "请输入文字信息";
"text_options.edit_text_title" = "编辑文字";
"text_options.edit_text_message" = "修改文字信息";
"text_options.text_display_placeholder" = "显示文字";
"text_options.text_value_placeholder" = "值";
"text_options.load_error" = "加载文字数据失败";
"text_options.add_error" = "添加文字失败";
"text_options.update_error" = "更新文字失败";
"text_options.delete_error" = "删除文字失败";

// 选项页面
"options.texts.title" = "文字";
"options.texts.subtitle" = "管理文字选项";

// 设置
"settings.appearance.options" = "选项管理";
"settings.appearance.options_subtitle" = "管理应用选项，如颜色、尺寸等"; 

"permission.denied.title" = "无法访问";
"permission.photos.denied.message" = "请在设置中允许访问照片，以便从相册中选择图片"; 