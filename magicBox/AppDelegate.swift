//
//  AppDelegate.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON>an on 2025/5/18.
//

import UIKit
 
@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> <PERSON><PERSON> {
        
        // 配置日志工具
        configureLogger()
        
        // 初始化数据库
        initializeDatabase()
        
        return true
    }
    
    // MARK: - 日志配置
    private func configureLogger() {
        // 配置日志输出选项
        Logger.configure(
            isEnabled: true,
            minimumLogLevel: .debug,
            showTimestamp: true,
            showFileInfo: false,
            showFunctionName: false,
            globalPrefix: "MagicBox",
            showGlobalPrefix: false
        )
        
        Logger.info("🚀 应用启动，日志系统已初始化", prefix: "AppDelegate")
        Logger.printConfiguration()
    }
    
    // MARK: - 数据库初始化
    private func initializeDatabase() {
        Logger.database.enter() // 使用数据库专用logger
        Logger.database.info("🗄️ 开始初始化数据库...")
        
        // 获取数据库管理器单例（这会触发数据库连接和表创建）
        let dbManager = DatabaseManager.shared
        
        // 检查数据库连接状态
        if dbManager.isConnected {
            Logger.database.info("✅ 数据库连接成功")
            
            // 打印数据库信息
            let dbInfo = dbManager.getDatabaseInfo()
            Logger.database.separator("数据库信息")
            for (key, value) in dbInfo {
                Logger.database.info("📊 \(key): \(value)")
            }
            Logger.database.separator()
            
            // 检查是否需要创建示例数据
            setupInitialDataIfNeeded()
            
        } else {
            Logger.database.error("❌ 数据库连接失败")
            // 这里可以添加错误处理逻辑，比如显示错误提示给用户
            showDatabaseErrorAlert()
        }
        
        Logger.database.exit() // 记录函数退出
    }
    
    // MARK: - 初始化数据设置
    private func setupInitialDataIfNeeded() {
        Logger.database.enter()
        
        do {
            let dbManager = DatabaseManager.shared
            
            // 检查是否已有模板数据
            let templates = try dbManager.getAllTemplates()
            
            if templates.isEmpty {
                Logger.database.info("📝 检测到空数据库，创建示例数据...")
                
                // 创建示例数据
                let databaseExample = DatabaseExample()
                databaseExample.setupExampleData()
                
                Logger.database.info("✅ 示例数据创建完成")
            } else {
                Logger.database.info("📚 数据库已有 \(templates.count) 个模板")
                
                // 使用格式化日志
                for template in templates {
                    Logger.database.debug("模板信息 - ID: %lld, 名称: %@", template.id!, template.name)
                }
            }
            
        } catch {
            Logger.database.error("❌ 检查初始数据时发生错误: \(error.localizedDescription)")
            // 记录错误对象详情
            Logger.database.object(error, name: "数据库错误")
        }
        
        Logger.database.exit()
    }
    
    // MARK: - 数据库错误处理
    private func showDatabaseErrorAlert() {
        Logger.ui.enter() // 使用UI专用logger
        Logger.ui.warning("准备显示数据库错误提示")
        
        // 在主线程中显示错误提示
        DispatchQueue.main.async {
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first else {
                Logger.ui.error("无法获取window来显示错误提示")
                return
            }
            
            let alert = UIAlertController(
                title: "数据库错误",
                message: "数据库初始化失败，应用可能无法正常工作。请重启应用或联系开发者。",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                Logger.ui.info("用户点击了数据库错误提示的确定按钮")
            })
            
            window.rootViewController?.present(alert, animated: true)
            Logger.ui.info("已显示数据库错误提示")
        }
        
        Logger.ui.exit()
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        Logger.debug("配置Scene连接", prefix: "SceneDelegate")
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        Logger.debug("丢弃Scene会话，数量: \(sceneSessions.count)", prefix: "SceneDelegate")
    }
}

// MARK: - 数据库管理扩展
extension AppDelegate {
    
    /// 获取数据库状态信息
    func getDatabaseStatus() -> String {
        Logger.database.enter()
        
        let dbManager = DatabaseManager.shared
        let status: String
        
        if dbManager.isConnected {
            let info = dbManager.getDatabaseInfo()
            status = "数据库正常运行，版本: \(info["version"] ?? "未知")"
            Logger.database.info("数据库状态检查: \(status)")
        } else {
            status = "数据库连接失败"
            Logger.database.error("数据库状态检查: \(status)")
        }
        
        Logger.database.exit()
        return status
    }
    
    /// 重置数据库（仅用于开发和测试）
    func resetDatabaseForDevelopment() {
        Logger.database.enter()
        Logger.database.warning("⚠️ 正在重置数据库（开发模式）", prefix: "DevTools")
        
        do {
            let dbManager = DatabaseManager.shared
            try dbManager.resetDatabase()
            Logger.database.info("🔄 数据库重置成功")
            
            // 重新创建示例数据
            setupInitialDataIfNeeded()
            
        } catch {
            Logger.database.error("❌ 数据库重置失败: \(error.localizedDescription)")
            Logger.database.object(error, name: "重置错误")
        }
        
        Logger.database.exit()
    }
    
    /// 演示不同前缀的使用
    func demonstratePrefixUsage() {
        Logger.separator("前缀功能演示", prefix: "Demo")
        
        // 使用预定义的业务Logger
        Logger.database.info("数据库操作日志")
        Logger.network.info("网络请求日志")
        Logger.ui.info("用户界面日志")
        Logger.settings.info("设置页面日志")
        Logger.auth.info("认证相关日志")
        Logger.storage.info("存储操作日志")
        Logger.analytics.info("统计分析日志")
        
        // 使用静态方法 + 临时前缀
        Logger.info("这是一个临时前缀的日志", prefix: "TempPrefix")
        
        // 使用全局函数 + 前缀
        #if DEBUG
        logDebug("使用全局函数的debug日志", prefix: "GlobalFunc")
        logInfo("使用全局函数的info日志", prefix: "GlobalFunc")
        #endif
        
        // 创建自定义业务Logger
        let customLogger = Logger(businessPrefix: "CustomModule")
        customLogger.info("自定义模块的日志")
        
        Logger.separator("演示结束", prefix: "Demo")
    }
}

