//
//  AppDelegate.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON>an on 2025/5/18.
//

import UIKit
import IQKeyboardManagerSwift

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> <PERSON><PERSON> {
        
        // 配置日志工具
        configureLogger()
        
        // 配置UI工具
        configureUI()
        
        // 初始化数据库
        initializeDatabase()
        
        return true
    }
    
    // MARK: - UI配置
    private func configureUI() {
        Logger.ui.enter()
        Logger.ui.info("🎨 开始配置UI工具...")
        
        // 配置IQKeyboardManager
        IQKeyboardManager.shared.isEnabled = true
        
        Logger.ui.info("✅ UI工具配置完成")
        Logger.ui.exit()
    }
    
    // MARK: - 日志配置
    private func configureLogger() {
        // 配置日志输出选项
        Logger.configure(
            isEnabled: true,
            minimumLogLevel: .debug,
            showTimestamp: true,
            showFileInfo: false,
            showFunctionName: false,
            globalPrefix: "MagicBox",
            showGlobalPrefix: false
        )
        
        Logger.info("🚀 应用启动，日志系统已初始化", prefix: "AppDelegate")
        Logger.printConfiguration()
    }
    
    // MARK: - 数据库初始化
    private func initializeDatabase() {
        Logger.database.enter() // 使用数据库专用logger
        Logger.database.info("🗄️ 开始初始化数据库...")
        
        // 获取数据库管理器单例（这会触发数据库连接和表创建）
        let dbManager = DatabaseManager.shared
        
        // 检查数据库连接状态
        if dbManager.isConnected {
            Logger.database.info("✅ 数据库连接成功")
            
            // 打印数据库信息
            let dbInfo = dbManager.getDatabaseInfo()
            Logger.database.separator("数据库信息")
            for (key, value) in dbInfo {
                Logger.database.info("📊 \(key): \(value)")
            }
            Logger.database.separator()
            
            // 检查是否需要创建示例数据
            setupInitialDataIfNeeded()
            
        } else {
            Logger.database.error("❌ 数据库连接失败")
            // 这里可以添加错误处理逻辑，比如显示错误提示给用户
            showDatabaseErrorAlert()
        }
        
        Logger.database.exit() // 记录函数退出
    }
    
    // MARK: - 初始化数据设置
    private func setupInitialDataIfNeeded() {
        Logger.database.enter()
        
        do {
            let dbManager = DatabaseManager.shared
            
            // 检查是否已有模板数据
            let templates = try dbManager.getAllTemplates()
            
            if templates.isEmpty {
                Logger.database.info("📝 检测到空数据库，创建示例数据...")
                
                // 创建示例数据
                let databaseExample = DatabaseExample()
                databaseExample.setupExampleData()
                
                Logger.database.info("✅ 示例数据创建完成")
            } else {
                Logger.database.info("📚 数据库已有 \(templates.count) 个模板")
                
                // 使用格式化日志
                for template in templates {
                    Logger.database.debug("模板信息 - ID: %lld, 名称: %@", template.id!, template.name)
                }
            }
            
        } catch {
            Logger.database.error("❌ 检查初始数据时发生错误: \(error.localizedDescription)")
            // 记录错误对象详情
            Logger.database.object(error, name: "数据库错误")
        }
        
        Logger.database.exit()
    }
    
    // MARK: - 数据库错误处理
    private func showDatabaseErrorAlert() {
        Logger.ui.enter() // 使用UI专用logger
        Logger.ui.warning("准备显示数据库错误提示")
        
        // 在主线程中显示错误提示
        DispatchQueue.main.async {
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first else {
                Logger.ui.error("无法获取window来显示错误提示")
                return
            }
            
            let alert = UIAlertController(
                title: "数据库错误",
                message: "数据库初始化失败，应用可能无法正常工作。请重启应用或联系开发者。",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                Logger.ui.info("用户点击了数据库错误提示的确定按钮")
            })
            
            window.rootViewController?.present(alert, animated: true)
            Logger.ui.info("已显示数据库错误提示")
        }
        
        Logger.ui.exit()
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        Logger.debug("配置Scene连接", prefix: "SceneDelegate")
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        Logger.debug("丢弃Scene会话，数量: \(sceneSessions.count)", prefix: "SceneDelegate")
    }
}


